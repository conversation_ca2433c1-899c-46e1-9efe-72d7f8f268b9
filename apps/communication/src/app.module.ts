import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CommonModule } from '@app/common';
import { HealthModule } from './health/health.module';
import * as basicAuth from 'express-basic-auth';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import { BullProviderModule } from '@app/common/bull/bull.module';
import { EmailConsumer } from './consumers/email.consumer';
import { NotificationProcessor } from './consumers/notification.consumer';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tenant } from '@app/common/typeorm/entities';
import { QUEUE_NAMES } from '@app/common/constants/queue.constants';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { TemplateService } from '@app/common/communication/services/template.service';
import { CircuitBreakerService } from '@app/common/communication/services/circuit-breaker.service';
import { SESEmailService } from '@app/common/communication/services/ses-email.service';
import { SESTemplateManagerService } from '@app/common/communication/services/ses-template-manager.service';
import {
    NotificationService,
    FirebaseNotificationProvider,
    WebPushNotificationProvider,
    InAppNotificationProvider
} from '@app/common/communication/services/notification.service';
import { TemplateManagementController } from './controllers/template-management.controller';
import { TestEmailController } from './controllers/test-email.controller';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { EmailAutomationService } from './services/email-automation.service';

@Module({
    imports: [
        CommonModule,
        ConfigModule.forRoot({
            isGlobal: true
        }),
        HealthModule,
        BullProviderModule,
        BullBoardModule.forFeature(
            { name: QUEUE_NAMES.EMAIL, adapter: BullMQAdapter },
            { name: QUEUE_NAMES.NOTIFICATION, adapter: BullMQAdapter },
            { name: QUEUE_NAMES.MULTI_CHANNEL, adapter: BullMQAdapter }
        ),
        BullBoardModule.forRoot({
            route: '/queues',
            adapter: ExpressAdapter,
            middleware: basicAuth({
                users: { ['admin']: process.env.BULLBOARD_PASSWORD || '' }, // Dynamic username and password
                challenge: true, // Display authentication dialog
                realm: 'LPM BullBoard' // Authentication realm
            })
        }),
        TypeOrmModule.forFeature([Tenant])
    ],
    controllers: [TemplateManagementController, TestEmailController],
    providers: [
        EmailConsumer,
        NotificationProcessor,
        TemplateService,
        CircuitBreakerService,
        SESEmailService,
        SESTemplateManagerService,
        MessageProducerService,
        NotificationService,
        FirebaseNotificationProvider,
        WebPushNotificationProvider,
        InAppNotificationProvider,
        EmailAutomationService
    ],
    exports: [EmailAutomationService, MessageProducerService, NotificationService, TemplateService]
})
export class AppModule {}
