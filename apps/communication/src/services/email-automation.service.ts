import { Injectable, Logger } from '@nestjs/common';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';

export interface StarterPackEmailVariables {
    tenantName: string;
    recipientName: string;
    clientId: string;
    caseId: string;
    caseNumber: string;
    documentLink: string;
    propertyType?: string;
    caseType?: string;
}

@Injectable()
export class EmailAutomationService {
    private readonly logger = new Logger(EmailAutomationService.name);

    constructor(
        private readonly messageProducer: MessageProducerService,
        private readonly tenantContext: TenantContextService
    ) {}

    async sendStarterPackEmail(
        _clientId: string,
        _caseId: string,
        clientEmail: string,
        variables: StarterPackEmailVariables,
        userId: string
    ): Promise<void> {
        const tenantId = this.tenantContext.getTenantId();

        if (!tenantId) {
            throw new Error('Tenant context is required for sending emails');
        }

        const emailJob = {
            tenantId,
            userId,
            channels: [COMMUNICATION_CHANNELS.EMAIL],
            recipients: {
                email: [clientEmail]
            },
            variables: {
                type: 'case-created' as const,
                tenantName: variables.tenantName,
                recipientName: variables.recipientName,
                caseId: variables.caseId,
                caseNumber: variables.caseNumber,
                caseType: variables.caseType
            },
            priority: 5,
            caseId: variables.caseId
        };

        try {
            const result = await this.messageProducer.enqueueMessage(emailJob);
            this.logger.log(
                `Starter pack email queued successfully for case ${variables.caseId}: ${result.jobId}`
            );
        } catch (error) {
            this.logger.error(
                `Failed to queue starter pack email for case ${variables.caseId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }
}
