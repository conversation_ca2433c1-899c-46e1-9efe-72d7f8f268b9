import { Module } from '@nestjs/common';
import { CommonModule } from '@app/common';
import { EmailAutomationService } from './services/email-automation.service';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { TemplateService } from '@app/common/communication/services/template.service';
import { NotificationService } from '@app/common/communication/services/notification.service';

/**
 * Communication Integration Module
 * This module exports communication services that can be imported by other modules
 * to use email automation, notifications, and messaging capabilities.
 */
@Module({
    imports: [CommonModule],
    providers: [
        EmailAutomationService,
        MessageProducerService,
        TemplateService,
        NotificationService
    ],
    exports: [EmailAutomationService, MessageProducerService, TemplateService, NotificationService]
})
export class CommunicationIntegrationModule {}
