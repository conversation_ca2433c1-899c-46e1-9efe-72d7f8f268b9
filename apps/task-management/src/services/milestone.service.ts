import { Injectable, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { MilestoneRepository } from '../repositories/milestone.repository';
import { Milestone, MilestoneStatus } from '@app/common/typeorm/entities/tenant/milestone.entity';
import { Task, TaskStatus } from '@app/common/typeorm/entities/tenant/task.entity';
import { TaskService } from './task.service';
import { CaseAuditService } from 'apps/case-management/src/services/case-audit.service';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';
import { instance as logger } from '@app/common/utils';

@Injectable()
export class MilestoneService {
    constructor(
        private readonly milestoneRepository: MilestoneRepository,
        @Inject(forwardRef(() => TaskService))
        private readonly taskService: TaskService,
        private readonly caseAuditService: CaseAuditService,
        private readonly tenantContext: TenantContextService
    ) {}

    async createMilestone(
        milestoneData: Partial<Milestone>,
        caseId: string,
        userId: string
    ): Promise<Milestone> {
        const milestone = this.milestoneRepository.create({
            ...milestoneData,
            caseId,
            createdBy: userId
        });

        const savedMilestone = await this.milestoneRepository.save(milestone);

        // Log milestone creation in audit trail
        await this.logMilestoneAction(caseId, CaseAuditAction.STATUS_CHANGED, userId, 'System', {
            action: 'MILESTONE_CREATED',
            milestoneId: savedMilestone.id,
            milestoneName: savedMilestone.name,
            isDefault: savedMilestone.isDefault
        });

        return savedMilestone;
    }

    async getMilestoneById(id: string): Promise<Milestone> {
        const milestone = await this.milestoneRepository.findByIdWithTasks(id);
        if (!milestone) {
            throw new NotFoundException(`Milestone with ID ${id} not found`);
        }
        return milestone;
    }

    async updateMilestoneProgress(milestoneId: string): Promise<void> {
        const milestone = await this.getMilestoneById(milestoneId);
        const totalTasks = milestone.tasks.length;
        const completedTasks = milestone.tasks.filter(
            (task) => task.status === TaskStatus.DONE
        ).length;

        milestone.progressPercentage = parseFloat(((completedTasks / totalTasks) * 100).toFixed(2));
        if (completedTasks === totalTasks) {
            milestone.status = MilestoneStatus.COMPLETED;
            milestone.completionDate = new Date();
        } else if (completedTasks > 0) {
            milestone.status = MilestoneStatus.IN_PROGRESS;
        }
        await this.milestoneRepository.save(milestone);
    }

    /**
     * Add a custom task to a milestone
     */
    async addTaskToMilestone(
        milestoneId: string,
        taskData: Partial<Task>,
        userId: string,
        userName: string
    ): Promise<Task> {
        const milestone = await this.getMilestoneById(milestoneId);

        // Create the task with milestone reference
        const taskWithMilestone = {
            ...taskData,
            milestoneId: milestoneId,
            caseId: milestone.caseId,
            isDefault: false // Custom tasks are not default
        };

        const newTask = await this.taskService.createTask(
            taskWithMilestone as any,
            userId,
            userName
        );

        // Log task addition to milestone
        await this.logMilestoneAction(
            milestone.caseId,
            CaseAuditAction.STATUS_CHANGED,
            userId,
            userName,
            {
                action: 'TASK_ADDED_TO_MILESTONE',
                milestoneId: milestoneId,
                milestoneName: milestone.name,
                taskId: newTask.id,
                taskTitle: newTask.title,
                isCustomTask: true
            }
        );

        // Update milestone progress after adding task
        await this.updateMilestoneProgress(milestoneId);

        return newTask;
    }

    /**
     * Get milestones with progress for a case
     */
    async getMilestonesWithProgress(caseId: string): Promise<any[]> {
        const milestones = await this.milestoneRepository.findByCaseId(caseId);

        return milestones.map((milestone) => {
            const totalTasks = milestone.tasks.length;
            const completedTasks = milestone.tasks.filter(
                (task) => task.status === TaskStatus.DONE
            ).length;
            const inProgressTasks = milestone.tasks.filter(
                (task) => task.status === TaskStatus.IN_PROGRESS
            ).length;

            const progressPercentage =
                totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

            // Update milestone status based on task completion
            let status = MilestoneStatus.PENDING;
            if (completedTasks === totalTasks && totalTasks > 0) {
                status = MilestoneStatus.COMPLETED;
            } else if (completedTasks > 0 || inProgressTasks > 0) {
                status = MilestoneStatus.IN_PROGRESS;
            }

            return {
                ...milestone,
                progressPercentage,
                status,
                taskSummary: {
                    total: totalTasks,
                    completed: completedTasks,
                    inProgress: inProgressTasks,
                    pending: totalTasks - completedTasks - inProgressTasks
                }
            };
        });
    }

    /**
     * Trigger milestone progress update when a task status changes
     */
    async onTaskStatusChanged(taskId: string, userId: string, userName: string): Promise<void> {
        // Get the task to find its milestone
        const task = await this.taskService.getTaskById(taskId, userId);

        if (task.milestoneId) {
            const oldMilestone = await this.getMilestoneById(task.milestoneId);
            const oldStatus = oldMilestone.status;
            const oldProgress = oldMilestone.progressPercentage;

            // Update milestone progress
            await this.updateMilestoneProgress(task.milestoneId);

            // Get updated milestone to check for changes
            const updatedMilestone = await this.getMilestoneById(task.milestoneId);

            // Log milestone progress change if significant
            if (
                oldStatus !== updatedMilestone.status ||
                Math.abs(oldProgress - updatedMilestone.progressPercentage) >= 5
            ) {
                await this.logMilestoneAction(
                    updatedMilestone.caseId,
                    CaseAuditAction.STATUS_CHANGED,
                    userId,
                    userName,
                    {
                        action: 'MILESTONE_PROGRESS_UPDATED',
                        milestoneId: task.milestoneId,
                        milestoneName: updatedMilestone.name,
                        oldStatus,
                        newStatus: updatedMilestone.status,
                        oldProgress,
                        newProgress: updatedMilestone.progressPercentage,
                        triggeredByTaskId: taskId
                    }
                );
            }
        }
    }

    /**
     * Get milestone statistics for a case
     */
    async getCaseStatistics(caseId: string): Promise<any> {
        return await this.milestoneRepository.getCaseStatistics(caseId);
    }

    /**
     * Log milestone-related actions to audit trail
     */
    private async logMilestoneAction(
        caseId: string,
        action: CaseAuditAction,
        userId: string,
        userName: string,
        metadata: any
    ): Promise<void> {
        try {
            await this.caseAuditService.logAction(
                caseId,
                action,
                userId,
                userName,
                'system', // IP address
                metadata
            );
        } catch (error) {
            logger.error(`Failed to log milestone action: ${error.message}`, error.stack);
        }
    }
}
