import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { CommonModule } from '@app/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CONTROLLERS } from './controllers';
import { SERVICES } from './services';
import { REPOSITORIES } from './repositories';
import { RoleHierarchyService } from '@app/common/roles/hierarchy';
import { Task, TaskDependency, TaskHistory, Milestone } from '@app/common/typeorm/entities';
import { TenantContextMiddleware } from 'apps/case-management/src/middleware/tenant-context.middleware';
import { CaseServiceModule } from 'apps/case-management/src/case-service.module';

@Module({
    imports: [
        CommonModule,
        // MultiTenancyModule.forRoot(),
        TypeOrmModule.forFeature([Task, TaskDependency, TaskHistory, Milestone]),
        CaseServiceModule // Import the CaseServiceModule to provide the CaseService
    ],
    controllers: [...CONTROLLERS],
    providers: [...SERVICES, ...REPOSITORIES, RoleHierarchyService],
    exports: [...SERVICES]
})
export class TaskManagementModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(TenantContextMiddleware).forRoutes('*'); // Apply to all routes
    }
}
