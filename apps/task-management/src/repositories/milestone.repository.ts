import { Injectable } from '@nestjs/common';
import { Repository, DataSource, FindManyOptions, FindOneOptions } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BaseTenantRepository } from '@app/common/multi-tenancy/base-tenant.repository';
import { Milestone, MilestoneStatus } from '@app/common/typeorm/entities/tenant/milestone.entity';

@Injectable()
export class MilestoneRepository extends BaseTenantRepository<Milestone> {
    constructor(@InjectRepository(Milestone) repository: Repository<Milestone>) {
        super(repository);
    }

    /**
     * Find milestones by case ID with their tasks
     */
    async findByCaseId(caseId: string): Promise<Milestone[]> {
        return this.find({
            where: { caseId },
            relations: ['tasks'],
            order: { sortOrder: 'ASC' }
        });
    }

    /**
     * Find milestone by ID with tasks
     */
    async findByIdWithTasks(id: string): Promise<Milestone | null> {
        return this.findOne({
            where: { id },
            relations: ['tasks']
        });
    }

    /**
     * Find default milestones
     */
    async findDefaultMilestones(): Promise<Milestone[]> {
        return this.find({
            where: { isDefault: true },
            order: { sortOrder: 'ASC' }
        });
    }

    /**
     * Find milestones by status
     */
    async findByStatus(status: MilestoneStatus): Promise<Milestone[]> {
        return this.find({
            where: { status },
            relations: ['tasks'],
            order: { sortOrder: 'ASC' }
        });
    }

    /**
     * Update milestone progress and status
     */
    async updateProgress(
        id: string,
        progressPercentage: number,
        status: MilestoneStatus,
        completionDate?: Date
    ): Promise<void> {
        const updateData: any = {
            progressPercentage,
            status,
            updatedAt: new Date()
        };

        if (completionDate) {
            updateData.completionDate = completionDate;
        }

        await this.update(id, updateData);
    }

    /**
     * Get milestone statistics for a case
     */
    async getCaseStatistics(caseId: string): Promise<{
        total: number;
        completed: number;
        inProgress: number;
        pending: number;
        averageProgress: number;
    }> {
        const milestones = await this.findByCaseId(caseId);

        const stats = {
            total: milestones.length,
            completed: milestones.filter((m) => m.status === MilestoneStatus.COMPLETED).length,
            inProgress: milestones.filter((m) => m.status === MilestoneStatus.IN_PROGRESS).length,
            pending: milestones.filter((m) => m.status === MilestoneStatus.PENDING).length,
            averageProgress: 0
        };

        if (stats.total > 0) {
            const totalProgress = milestones.reduce(
                (sum, m) => sum + (m.progressPercentage || 0),
                0
            );
            stats.averageProgress = Math.round((totalProgress / stats.total) * 100) / 100;
        }

        return stats;
    }
}
