import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { CommonModule, ConfigModule } from '@app/common';
import { ApiResponseModule } from '@app/common/api-response';
import { HealthModule } from './health/health.module';
import { TenantMigrationController } from './controllers/tenant-migration.controller';
import * as cookieParser from 'cookie-parser';
import { KeycloakService } from './services/keycloak.service';
import { KeycloakHttpService } from './services/keycloak-http.service';
import { PublicSchemaMigrationService } from '@app/common/typeorm/migrations/public-schema.migrations';
import { RepositoriesModule } from '../../../libs/common/src/repositories/repositories.module';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { BullProviderModule } from '@app/common/bull/bull.module';

@Module({
    imports: [
        ConfigModule,
        CommonModule,
        ApiResponseModule.forRoot({
            excludePaths: ['/health', '/health/db', '/ping', '/metrics']
        }),
        HealthModule,
        RepositoriesModule,
        BullProviderModule
    ],
    controllers: [TenantMigrationController, AuthController],
    providers: [
        KeycloakService,
        KeycloakHttpService,
        PublicSchemaMigrationService,
        AuthService,
        JwtGuard
    ],
    exports: [AuthService, JwtGuard]
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        // Apply cookie-parser middleware to all routes
        consumer.apply(cookieParser()).forRoutes('*');
    }
}
