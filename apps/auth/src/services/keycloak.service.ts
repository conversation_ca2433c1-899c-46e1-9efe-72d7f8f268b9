import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KeycloakHttpService, KeycloakErrorType } from './keycloak-http.service';
import { AuthConfig } from '@app/common/config/interfaces';
import { HttpStatusCode } from '@app/common/enums/http-status.enum';
import {
    KeycloakRealmRepresentation,
    KeycloakUserRepresentation,
    KeycloakRoleRepresentation,
    KeycloakClientRepresentation,
    KeycloakRealmAdminCredentials
} from '../interfaces/keycloak-admin.interface';
import {
    KeycloakTokenResponse,
    KeycloakTokenRequest
} from '../interfaces/keycloak-token.interface';
import { JwksResponse, OpenIdConfiguration } from '../interfaces/jwks.interface';
import { CreateTenantDto } from '../dto/create-tenant.dto';
import { CreateUserDto } from '../dto/create-user.dto';
import { CreateRoleDto } from '../dto/create-role.dto';
import { AssignRoleDto } from '../dto/assign-role.dto';

/**
 * Cache entry for a token
 */
interface TokenCacheEntry {
    token: string;
    expiry: number;
    refreshToken?: string;
}

/**
 * Service for interacting with Keycloak Admin API
 */
@Injectable()
export class KeycloakService {
    private readonly logger = new Logger(KeycloakService.name);
    private readonly authConfig: AuthConfig;
    private readonly tokenCache: Map<string, TokenCacheEntry> = new Map();
    private realmAdminCredentials: Map<string, KeycloakRealmAdminCredentials> = new Map();

    // Cache refresh threshold in seconds - refresh token if it expires within this time
    private readonly tokenRefreshThreshold = 60;

    constructor(
        private readonly configService: ConfigService,
        private readonly keycloakHttp: KeycloakHttpService
    ) {
        this.authConfig = this.configService.get<AuthConfig>('auth')!;

        // Start automated processes
        this.initialize();
    }

    /**
     * Initialize the service with all recurring tasks and health checks
     */
    private async initialize(): Promise<void> {
        // Perform a comprehensive server check first with diagnostics
        await this.validateKeycloakServer();

        // Start Keycloak health checks
        this.keycloakHttp.startPeriodicHealthCheck(60000); // Every minute

        // Schedule token cache cleanup
        this.scheduleTokenCacheCleanup(3600000); // Every hour

        // Initial token cache cleanup (in case service was restarted)
        this.cleanupTokenCache();

        this.logger.log(
            'KeycloakService initialized with periodic health checks and cache maintenance'
        );
    }

    /**
     * Performs a comprehensive validation of the Keycloak server
     * with diagnostic information when issues are detected
     */
    private async validateKeycloakServer(): Promise<void> {
        this.logger.log(`Validating Keycloak server at ${this.authConfig.serverUrl}`);

        try {
            // Check if Keycloak is reachable
            const health = await this.keycloakHttp.checkHealth();

            if (health.status === 'UP') {
                this.logger.log('Keycloak server is healthy and responding');

                // Try to get an admin token as a deeper check
                try {
                    await this.getAdminToken();
                    this.logger.log('Successfully authenticated with admin credentials');
                } catch (authError) {
                    this.logger.error(
                        'Server is up but admin authentication failed - check admin credentials',
                        authError
                    );
                }
            } else {
                this.logger.warn(
                    `Keycloak server health check failed: ${JSON.stringify(health.details)}`
                );

                // Try some additional diagnostic checks
                await this.performKeycloakDiagnostics();
            }
        } catch (error) {
            this.logger.error('Failed to validate Keycloak server', error);

            // Try some additional diagnostic checks
            await this.performKeycloakDiagnostics();
        }
    }

    /**
     * Perform diagnostics on the Keycloak connection
     */
    private async performKeycloakDiagnostics(): Promise<void> {
        this.logger.log('Performing Keycloak diagnostics...');

        // Check basic connectivity
        try {
            // Try a simple HTTP request to the server root
            const response = await this.keycloakHttp.getInstance().get('/', {
                validateStatus: () => true, // Accept any status code for diagnostics
                timeout: 3000
            });

            this.logger.log(`Server root response: Status ${response.status}`);

            if (
                response.status >= HttpStatusCode.OK &&
                response.status < HttpStatusCode.MULTIPLE_CHOICES
            ) {
                this.logger.log('Server is responding to base URL requests');
            } else {
                this.logger.warn(`Server responded with non-success status: ${response.status}`);
            }
        } catch (error) {
            this.logger.error('Cannot connect to Keycloak server root URL', error);
            this.logger.warn(`Check if Keycloak is running at ${this.authConfig.serverUrl}`);
        }

        // Log key configuration values (without sensitive data)
        this.logger.log('Current Keycloak configuration:');
        this.logger.log(`- Server URL: ${this.authConfig.serverUrl}`);
        this.logger.log(`- Admin username: ${this.authConfig.admin}`);
        this.logger.log(
            `- Admin password: ${'*'.repeat(this.authConfig.adminPassword?.length || 0)}`
        );
    }

    /**
     * Gets a cached token or retrieves a new one if expired
     * @param cacheKey The key to use for caching
     * @param fetchTokenFn Function to fetch a new token if needed
     * @param refreshTokenFn Optional function to refresh a token if possible
     */
    private async getCachedToken(
        cacheKey: string,
        fetchTokenFn: () => Promise<KeycloakTokenResponse>,
        refreshTokenFn?: (refreshToken: string) => Promise<KeycloakTokenResponse>
    ): Promise<string> {
        const now = Math.floor(Date.now() / 1000);
        const cachedEntry = this.tokenCache.get(cacheKey);

        // If we have a valid token not close to expiry, return it
        if (cachedEntry?.token && cachedEntry.expiry > now + this.tokenRefreshThreshold) {
            this.logger.debug(
                `Using cached token for ${cacheKey} valid for ${cachedEntry.expiry - now}s`
            );
            return cachedEntry.token;
        }

        // If we have a refresh token and a refresh function, try to refresh
        if (cachedEntry?.refreshToken && refreshTokenFn && cachedEntry.expiry > now - 3600) {
            try {
                this.logger.debug(`Attempting to refresh token for ${cacheKey}`);
                const response = await refreshTokenFn(cachedEntry.refreshToken);
                const tokenEntry: TokenCacheEntry = {
                    token: response.access_token,
                    expiry: now + response.expires_in,
                    refreshToken: response.refresh_token
                };
                this.tokenCache.set(cacheKey, tokenEntry);
                return tokenEntry.token;
            } catch (error) {
                this.logger.warn(
                    `Failed to refresh token for ${cacheKey}, will fetch new token`,
                    error
                );
                // Continue to fetch a new token
            }
        }

        // Fetch a new token
        this.logger.debug(`Fetching new token for ${cacheKey}`);
        const response = await fetchTokenFn();
        const tokenEntry: TokenCacheEntry = {
            token: response.access_token,
            expiry: now + response.expires_in,
            refreshToken: response.refresh_token
        };
        this.tokenCache.set(cacheKey, tokenEntry);
        return tokenEntry.token;
    }

    /**
     * Gets an admin access token for the master realm
     * Used for administrative operations like creating realms and initial setup
     */
    private async getAdminToken(): Promise<string> {
        const cacheKey = 'admin:master';

        const fetchToken = async () => {
            try {
                return await this.keycloakHttp.post<KeycloakTokenResponse>(
                    '/realms/master/protocol/openid-connect/token',
                    new URLSearchParams({
                        grant_type: 'password',
                        client_id: 'admin-cli',
                        username: this.authConfig.admin,
                        password: this.authConfig.adminPassword
                    })
                );
            } catch (error) {
                this.logger.error('Failed to get admin token', error);
                throw new UnauthorizedException('Failed to authenticate with Keycloak admin');
            }
        };

        const refreshToken = async (refreshToken: string) => {
            return this.keycloakHttp.post<KeycloakTokenResponse>(
                '/realms/master/protocol/openid-connect/token',
                new URLSearchParams({
                    grant_type: 'refresh_token',
                    client_id: 'admin-cli',
                    refresh_token: refreshToken
                })
            );
        };

        return this.getCachedToken(cacheKey, fetchToken, refreshToken);
    }

    /**
     * Gets a realm-specific admin token
     * Used for administrative operations within a specific realm
     * @param realm The realm to get an admin token for
     * @param username The admin username
     * @param password The admin password
     * @param clientId The client ID to use
     * @param clientSecret The client secret to use
     * @returns The realm admin token
     */
    private async getRealmAdminToken(
        realm: string,
        username: string,
        password: string,
        clientId: string,
        clientSecret: string
    ): Promise<string> {
        const cacheKey = `admin:${realm}:${username}:${clientId}`;

        const fetchToken = async () => {
            try {
                // Get a new token
                return await this.keycloakHttp.post<KeycloakTokenResponse>(
                    `/realms/${realm}/protocol/openid-connect/token`,
                    new URLSearchParams({
                        grant_type: 'password',
                        client_id: clientId,
                        client_secret: clientSecret,
                        username,
                        password,
                        scope: 'openid offline_access'
                    })
                );
            } catch (error) {
                this.logger.error(`Failed to get realm admin token for realm: ${realm}`, error);
                throw new UnauthorizedException(
                    `Failed to authenticate with realm admin: ${error.message}`
                );
            }
        };

        const refreshToken = async (refreshToken: string) => {
            return this.keycloakHttp.post<KeycloakTokenResponse>(
                `/realms/${realm}/protocol/openid-connect/token`,
                new URLSearchParams({
                    grant_type: 'refresh_token',
                    client_id: clientId,
                    client_secret: clientSecret,
                    refresh_token: refreshToken
                })
            );
        };

        const token = await this.getCachedToken(cacheKey, fetchToken, refreshToken);

        // Store the credentials for future use
        if (!this.realmAdminCredentials.has(realm)) {
            this.realmAdminCredentials.set(realm, {
                realm,
                username,
                clientId,
                clientSecret,
                accessToken: null,
                tokenExpiry: 0
            });
        }

        return token;
    }

    /**
     * Gets the appropriate admin token for a realm
     * Tries to use realm-specific admin token if available, otherwise falls back to master admin token
     * @param realm The realm to get an admin token for
     * @param adminPassword Optional admin password for realm-specific admin token
     * @returns The admin token to use for API calls
     */
    private async getAppropriateAdminToken(realm: string, adminPassword?: string): Promise<string> {
        // Try to get realm-specific admin token if available
        const realmCredentials = this.realmAdminCredentials.get(realm);

        if (realmCredentials && adminPassword) {
            try {
                // Use the realm admin credentials
                const token = await this.getRealmAdminToken(
                    realmCredentials.realm,
                    realmCredentials.username,
                    adminPassword,
                    realmCredentials.clientId,
                    realmCredentials.clientSecret
                );
                this.logger.log(`Using realm-specific admin token for realm: ${realm}`);
                return token;
            } catch (error) {
                // Fall back to master admin token
                this.logger.warn(
                    `Failed to get realm admin token, falling back to master admin: ${error.message}`
                );
                return await this.getAdminToken();
            }
        } else {
            // No realm-specific admin credentials or no password provided, use master admin token
            return await this.getAdminToken();
        }
    }

    /**
     * Creates a new realm with default security settings
     * @param createTenantDto The tenant creation DTO
     * @returns The created realm representation
     */
    async createRealm(createTenantDto: CreateTenantDto): Promise<KeycloakRealmRepresentation> {
        // For realm creation, we always need the master admin token
        const adminToken = await this.getAdminToken();

        // Use the provided settings or fall back to defaults
        const realmRepresentation: KeycloakRealmRepresentation = {
            realm: createTenantDto.realm,
            displayName: createTenantDto.displayName,
            enabled: true,
            // Security settings
            sslRequired: 'external',
            bruteForceProtected: true,
            permanentLockout: false,
            maxFailureWaitSeconds: 900,
            minimumQuickLoginWaitSeconds: 60,
            waitIncrementSeconds: 60,
            quickLoginCheckMilliSeconds: 1000,
            maxDeltaTimeSeconds: 43200,
            failureFactor: 3,
            // Authentication settings
            registrationAllowed: createTenantDto.registrationAllowed ?? false,
            registrationEmailAsUsername: true,
            rememberMe: createTenantDto.rememberMe ?? true,
            verifyEmail: createTenantDto.verifyEmail ?? true,
            loginWithEmailAllowed: true,
            duplicateEmailsAllowed: false,
            resetPasswordAllowed: true,
            editUsernameAllowed: false,
            // Token settings
            revokeRefreshToken: true,
            refreshTokenMaxReuse: 0,
            ssoSessionIdleTimeout: 1800,
            ssoSessionMaxLifespan: 36000,
            offlineSessionIdleTimeout: 2592000,
            accessTokenLifespan: 300,
            accessTokenLifespanForImplicitFlow: 900,
            actionTokenGeneratedByAdminLifespan: 43200,
            actionTokenGeneratedByUserLifespan: 300
        };

        // Maximum number of retry attempts for creating a realm
        const maxRetries = 3;
        let retryCount = 0;
        let lastError: Error | null = null;

        while (retryCount < maxRetries) {
            try {
                this.logger.log(
                    `Creating realm: ${createTenantDto.realm} with dedicated admin: ${createTenantDto.dedicatedRealmAdmin !== false}`
                );

                if (retryCount > 0) {
                    this.logger.log(
                        `Retry attempt ${retryCount} for creating realm ${createTenantDto.realm}`
                    );
                }

                // Add request ID for tracing
                const requestId = `create-realm-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

                await this.keycloakHttp.post('/admin/realms', realmRepresentation, {
                    headers: {
                        Authorization: `Bearer ${adminToken}`,
                        'X-Request-ID': requestId
                    }
                });

                // After successful creation, verify the realm exists
                try {
                    await this.verifyRealmExists(createTenantDto.realm, adminToken);
                    this.logger.log(
                        `Successfully verified realm creation: ${createTenantDto.realm}`
                    );
                    return realmRepresentation;
                } catch (verifyError) {
                    // If verification fails, we'll retry
                    this.logger.warn(
                        `Realm creation appeared successful but verification failed for ${createTenantDto.realm}: ${verifyError.message}`
                    );
                    lastError = verifyError;
                    retryCount++;

                    // Wait before retrying (exponential backoff)
                    const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
                    await new Promise((resolve) => setTimeout(resolve, delay));
                    continue;
                }
            } catch (error) {
                // Check for specific error types that might be recoverable
                if (this.isRecoverableError(error)) {
                    this.logger.warn(
                        `Recoverable error during realm creation for ${createTenantDto.realm}: ${error.message}`
                    );
                    lastError = error;
                    retryCount++;

                    // Wait before retrying (exponential backoff)
                    const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
                    await new Promise((resolve) => setTimeout(resolve, delay));
                } else {
                    // Non-recoverable error, throw immediately
                    this.logger.error(`Failed to create realm: ${createTenantDto.realm}`, error);
                    throw new Error(`Failed to create realm: ${error.message}`);
                }
            }
        }

        // If we've exhausted all retries, throw the last error
        this.logger.error(
            `Failed to create realm after ${maxRetries} attempts: ${createTenantDto.realm}`,
            lastError
        );
        throw new Error(`Failed to create realm after multiple attempts: ${lastError?.message}`);
    }

    /**
     * Verifies that a realm exists
     * @param realm The realm name to verify
     * @param adminToken The admin token to use
     */
    private async verifyRealmExists(realm: string, adminToken: string): Promise<void> {
        try {
            await this.keycloakHttp.get(`/admin/realms/${realm}`, {
                headers: { Authorization: `Bearer ${adminToken}` }
            });
        } catch (error) {
            throw new Error(`Realm verification failed: ${error.message}`);
        }
    }

    /**
     * Determines if an error is potentially recoverable
     * @param error The error to check
     * @returns True if the error is recoverable
     */
    private isRecoverableError(error: any): boolean {
        // Conflict errors (resource already exists) are sometimes recoverable
        if (error.type === KeycloakErrorType.CLIENT && error.status === HttpStatusCode.CONFLICT) {
            return true;
        }

        // Network errors, server overloaded, or temporary service disruptions are recoverable
        if (
            error.type === KeycloakErrorType.NETWORK ||
            error.type === KeycloakErrorType.TIMEOUT ||
            error.type === KeycloakErrorType.SERVER
        ) {
            return true;
        }

        // 5xx errors are typically recoverable
        if (error.status && error.status >= HttpStatusCode.INTERNAL_SERVER_ERROR) {
            return true;
        }

        return false;
    }

    /**
     * Creates a new client for a realm with admin capabilities
     * @param realm The realm to create the client in
     * @param clientId The client ID
     * @param clientName The client name
     * @returns The created client with its secret
     */
    async createRealmAdminClient(
        realm: string,
        clientId: string,
        clientName: string
    ): Promise<KeycloakClientRepresentation> {
        try {
            // For client creation, we need the master admin token
            const adminToken = await this.getAdminToken();

            // Create a confidential client with service account enabled
            const clientRepresentation: KeycloakClientRepresentation = {
                clientId,
                name: clientName,
                enabled: true,
                publicClient: false,
                serviceAccountsEnabled: true,
                directAccessGrantsEnabled: true,
                standardFlowEnabled: true,
                implicitFlowEnabled: false,
                fullScopeAllowed: false,
                clientAuthenticatorType: 'client-secret',
                redirectUris: ['*'],
                webOrigins: ['*'],
                protocol: 'openid-connect'
            };

            await this.keycloakHttp.post(`/admin/realms/${realm}/clients`, clientRepresentation, {
                headers: {
                    Authorization: `Bearer ${adminToken}`
                }
            });

            // Get the created client to retrieve its ID
            const clients = await this.keycloakHttp.get<KeycloakClientRepresentation[]>(
                `/admin/realms/${realm}/clients?clientId=${clientId}&exact=true`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            if (!clients || clients.length === 0) {
                throw new Error('Client was created but could not be retrieved');
            }

            const createdClient = clients[0];

            // Get the client secret
            const clientSecret = await this.keycloakHttp.get<{ value: string }>(
                `/admin/realms/${realm}/clients/${createdClient.id}/client-secret`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            // Add the secret to the client representation
            createdClient.secret = clientSecret.value;

            // Get the realm-management client ID to assign roles
            const realmManagementClients = await this.keycloakHttp.get<
                KeycloakClientRepresentation[]
            >(`/admin/realms/${realm}/clients?clientId=realm-management&exact=true`, {
                headers: {
                    Authorization: `Bearer ${adminToken}`
                }
            });

            if (!realmManagementClients || realmManagementClients.length === 0) {
                throw new Error('Could not find realm-management client');
            }

            const realmManagementClientId = realmManagementClients[0].id;

            // Get the realm-admin role
            const realmAdminRole = await this.keycloakHttp.get(
                `/admin/realms/${realm}/clients/${realmManagementClientId}/roles/realm-admin`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            // Get the service account user ID
            const serviceAccount = await this.keycloakHttp.get<KeycloakUserRepresentation>(
                `/admin/realms/${realm}/clients/${createdClient.id}/service-account-user`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            // Assign the realm-admin role to the service account
            await this.keycloakHttp.post(
                `/admin/realms/${realm}/users/${serviceAccount.id}/role-mappings/clients/${realmManagementClientId}`,
                [realmAdminRole],
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            return createdClient;
        } catch (error) {
            this.logger.error(`Failed to create admin client in realm: ${realm}`, error);
            throw new Error(`Failed to create admin client: ${error.message}`);
        }
    }

    /**
     * Creates initial admin user and client for a new realm
     * @param realm The realm to create the admin user in
     * @param createTenantDto The tenant creation DTO
     * @returns The created user and client
     */
    async createInitialAdminUser(
        realm: string,
        createTenantDto: CreateTenantDto
    ): Promise<{ user: KeycloakUserRepresentation; client: KeycloakClientRepresentation }> {
        // For initial setup, we always use the master admin token
        const adminToken = await this.getAdminToken();
        const isDedicatedRealmAdmin = createTenantDto.dedicatedRealmAdmin !== false; // Default to true if not specified

        try {
            // 1. Create dedicated client for the realm
            // Use a public client configuration to avoid client secret issues
            const clientRepresentation: KeycloakClientRepresentation = {
                clientId: `${realm}-admin-client`,
                name: `${realm} Admin Client`,
                enabled: true,
                standardFlowEnabled: true,
                directAccessGrantsEnabled: true, // Enable Resource Owner Password Credentials Grant
                serviceAccountsEnabled: false, // Not needed for public clients
                protocol: 'openid-connect',
                publicClient: true, // Set to true to avoid client secret issues
                bearerOnly: false,
                // Add additional configuration to ensure proper access
                implicitFlowEnabled: false,
                fullScopeAllowed: true,
                defaultClientScopes: ['web-origins', 'acr', 'profile', 'roles', 'email'],
                optionalClientScopes: ['address', 'phone', 'offline_access', 'microprofile-jwt'],
                webOrigins: ['*'],
                redirectUris: ['*']
            };

            // Create the client
            await this.keycloakHttp.post<KeycloakClientRepresentation>(
                `/admin/realms/${realm}/clients`,
                clientRepresentation,
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );

            // Get the created client to retrieve its ID
            const clients = await this.keycloakHttp.get<KeycloakClientRepresentation[]>(
                `/admin/realms/${realm}/clients?clientId=${clientRepresentation.clientId}&exact=true`,
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );

            if (!clients || clients.length === 0) {
                throw new Error('Client was created but could not be retrieved');
            }

            const createdClient = clients[0];
            createdClient.secret = clientRepresentation.secret;

            // Re-apply the client configuration to ensure all settings are properly set
            this.logger.log(
                `Re-applying client configuration for ${clientRepresentation.clientId}`
            );

            // Merge the original configuration with the retrieved client ID
            const updatedClientConfig = {
                ...clientRepresentation,
                id: createdClient.id,
                // Explicitly set critical settings
                directAccessGrantsEnabled: true,
                publicClient: true,
                standardFlowEnabled: true,
                implicitFlowEnabled: false,
                serviceAccountsEnabled: false,
                // Add additional settings for proper authorization
                fullScopeAllowed: true,
                // Ensure proper protocol settings
                protocol: 'openid-connect',
                // Ensure proper redirect URIs and web origins
                redirectUris: ['*'],
                webOrigins: ['*']
            };

            // Update the client configuration
            await this.keycloakHttp.put(
                `/admin/realms/${realm}/clients/${createdClient.id}`,
                updatedClientConfig,
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );

            this.logger.log(`Client configuration re-applied for ${clientRepresentation.clientId}`);

            // Verify the client configuration was applied correctly
            const verifiedClient = await this.keycloakHttp.get<KeycloakClientRepresentation>(
                `/admin/realms/${realm}/clients/${createdClient.id}`,
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );

            this.logger.log(
                `Verified client configuration: directAccessGrantsEnabled=${verifiedClient.directAccessGrantsEnabled}, publicClient=${verifiedClient.publicClient}`
            );

            // For public clients, we don't need a client secret
            if (clientRepresentation.publicClient) {
                this.logger.log(
                    `Client ${clientRepresentation.clientId} is a public client, no secret needed`
                );
                createdClient.secret = '';
            } else {
                // For confidential clients, get the client secret
                const clientSecret = await this.keycloakHttp.get<{ value: string }>(
                    `/admin/realms/${realm}/clients/${createdClient.id}/client-secret`,
                    { headers: { Authorization: `Bearer ${adminToken}` } }
                );

                createdClient.secret = clientSecret.value;
                this.logger.log(
                    `Retrieved client secret for ${clientRepresentation.clientId}: ${createdClient.secret}`
                );
            }

            // 2. Create admin user
            // According to Keycloak documentation, we need to ensure:
            // 1. User is enabled
            // 2. Email is verified
            // 3. Password is not temporary
            // 4. No required actions are set
            const userRepresentation: KeycloakUserRepresentation = {
                username: createTenantDto.adminUsername,
                email: createTenantDto.adminEmail,
                firstName: createTenantDto.adminFirstName,
                lastName: createTenantDto.adminLastName,
                enabled: true,
                emailVerified: true,
                // Set credentials with non-temporary password
                credentials: [
                    {
                        type: 'password',
                        value: createTenantDto.adminPassword,
                        temporary: false
                    }
                ],
                // Add attributes for additional configuration
                attributes: {
                    isRealmAdmin: ['true']
                },
                // Explicitly set empty required actions
                requiredActions: []
            };

            this.logger.log(`Creating admin user with username: ${createTenantDto.adminUsername}`);
            this.logger.log(
                `Admin user will have password: ${createTenantDto.adminPassword.substring(0, 2)}${'*'.repeat(createTenantDto.adminPassword.length - 2)}`
            );
            this.logger.log(`Admin user will be enabled: ${userRepresentation.enabled}`);
            this.logger.log(
                `Admin user will have verified email: ${userRepresentation.emailVerified}`
            );

            // Create the user and get the user ID from the response headers
            this.logger.log(`Creating admin user: ${createTenantDto.adminUsername}`);

            // Use our new method to create the user and get the ID
            const createdUser = await this.createUserAndGetId(
                realm,
                userRepresentation,
                adminToken
            );

            // Explicitly set the user's password using the reset-password endpoint
            this.logger.log(
                `Setting password for user ${createTenantDto.adminUsername} (ID: ${createdUser.id})`
            );
            await this.keycloakHttp.put(
                `/admin/realms/${realm}/users/${createdUser.id}/reset-password`,
                {
                    type: 'password',
                    temporary: false,
                    value: createTenantDto.adminPassword
                },
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );
            this.logger.log(`Password set successfully for user ${createTenantDto.adminUsername}`);

            // 3. Assign realm-management roles to admin user
            const realmManagementClient = await this.keycloakHttp.get<
                KeycloakClientRepresentation[]
            >(`/admin/realms/${realm}/clients?clientId=realm-management`, {
                headers: { Authorization: `Bearer ${adminToken}` }
            });

            if (!realmManagementClient?.[0]?.id) {
                throw new Error('Could not find realm-management client');
            }

            // Get all realm-management roles
            const realmManagementRoles = await this.keycloakHttp.get<KeycloakRoleRepresentation[]>(
                `/admin/realms/${realm}/clients/${realmManagementClient[0].id}/roles`,
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );

            // Assign all realm-management roles to admin user
            this.logger.log(`Assigning realm-management roles to user ID: ${createdUser.id}`);
            try {
                await this.keycloakHttp.post(
                    `/admin/realms/${realm}/users/${createdUser.id}/role-mappings/clients/${realmManagementClient[0].id}`,
                    realmManagementRoles,
                    { headers: { Authorization: `Bearer ${adminToken}` } }
                );
                this.logger.log('Successfully assigned realm-management roles');
            } catch (error) {
                this.logger.error(
                    `Failed to assign realm-management roles: ${error.message}`,
                    error
                );
                throw new Error(`Failed to assign realm-management roles: ${error.message}`);
            }

            // 4. Assign default realm roles
            this.logger.log('Assigning default realm roles');
            const defaultRoles = ['offline_access', 'uma_authorization', 'admin'];
            try {
                for (const roleName of defaultRoles) {
                    this.logger.log(`Getting role: ${roleName}`);
                    const role = await this.keycloakHttp.get<KeycloakRoleRepresentation>(
                        `/admin/realms/${realm}/roles/${roleName}`,
                        { headers: { Authorization: `Bearer ${adminToken}` } }
                    );

                    this.logger.log(`Assigning role ${roleName} to user ID: ${createdUser.id}`);
                    await this.keycloakHttp.post(
                        `/admin/realms/${realm}/users/${createdUser.id}/role-mappings/realm`,
                        [role],
                        { headers: { Authorization: `Bearer ${adminToken}` } }
                    );
                }
                this.logger.log('Successfully assigned default realm roles');
            } catch (error) {
                this.logger.error(`Failed to assign default realm roles: ${error.message}`, error);
                // Continue even if default roles fail - they might not be critical
                this.logger.warn('Continuing despite failure to assign default realm roles');
            }

            // 5. If this is a dedicated realm admin, we don't need to do anything else
            // The admin already has full privileges within this realm only

            // 6. Store the realm admin credentials for future use
            if (isDedicatedRealmAdmin) {
                const realmCredentials: KeycloakRealmAdminCredentials = {
                    realm,
                    username: createTenantDto.adminUsername,
                    clientId: clientRepresentation.clientId,
                    clientSecret: clientRepresentation.secret || '',
                    accessToken: null,
                    tokenExpiry: 0
                };

                this.realmAdminCredentials.set(realm, realmCredentials);

                this.logger.log(`Created dedicated realm admin for realm: ${realm}`);
            } else {
                // If not a dedicated realm admin, we need to grant master realm access
                // This is typically not recommended for security reasons
                this.logger.warn(
                    `Creating non-dedicated realm admin for realm: ${realm} - this grants cross-realm access`
                );
            }
            return { user: createdUser, client: createdClient };
        } catch (error) {
            this.logger.error(`Failed to setup realm ${realm}`, error);
            throw new Error(`Failed to setup realm: ${error.message}`);
        }
    }

    /**
     * Creates a new user in a realm
     * Uses realm-specific admin token if available, otherwise falls back to master admin token
     * @param createUserDto The user creation DTO
     * @returns The created user
     */
    async createUser(
        createUserDto: CreateUserDto,
        realm: string
    ): Promise<KeycloakUserRepresentation> {
        // Get the appropriate admin token
        const adminToken = await this.getAppropriateAdminToken(realm, createUserDto.adminPassword);

        const userRepresentation: KeycloakUserRepresentation = {
            username: createUserDto.username,
            email: createUserDto.email,
            enabled: createUserDto.enabled ?? true,
            emailVerified: createUserDto.emailVerified ?? true, // Default to true to allow immediate login
            firstName: createUserDto.firstName,
            lastName: createUserDto.lastName,
            credentials: [
                {
                    type: 'password',
                    value: createUserDto.password,
                    temporary: createUserDto.temporaryPassword ?? false
                }
            ],
            requiredActions: createUserDto.temporaryPassword ? ['UPDATE_PASSWORD'] : []
        };

        try {
            // Create the user and get the user ID from the response headers
            this.logger.log(`Creating user: ${createUserDto.username}`);
            this.logger.log(`User will be enabled: ${userRepresentation.enabled}`);
            this.logger.log(`User will have verified email: ${userRepresentation.emailVerified}`);
            this.logger.log(
                `User will have temporary password: ${createUserDto.temporaryPassword ?? false}`
            );

            // Use our new method to create the user and get the ID
            const createdUser = await this.createUserAndGetId(
                realm,
                userRepresentation,
                adminToken
            );

            // Explicitly set the user's password using the reset-password endpoint
            this.logger.log(
                `Setting password for user ${createUserDto.username} (ID: ${createdUser.id})`
            );
            await this.keycloakHttp.put(
                `/admin/realms/${realm}/users/${createdUser.id}/reset-password`,
                {
                    type: 'password',
                    temporary: createUserDto.temporaryPassword ?? false,
                    value: createUserDto.password
                },
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );
            this.logger.log(`Password set successfully for user ${createUserDto.username}`);

            // Assign default roles if specified
            if (createUserDto.roles?.length) {
                this.logger.log(`Assigning roles to user: ${createdUser.id}`);
                try {
                    const roles = await Promise.all(
                        createUserDto.roles.map((roleName: string) =>
                            this.keycloakHttp.get<KeycloakRoleRepresentation>(
                                `/admin/realms/${realm}/roles/${roleName}`,
                                { headers: { Authorization: `Bearer ${adminToken}` } }
                            )
                        )
                    );

                    await this.keycloakHttp.post(
                        `/admin/realms/${realm}/users/${createdUser.id}/role-mappings/realm`,
                        roles,
                        { headers: { Authorization: `Bearer ${adminToken}` } }
                    );
                    this.logger.log('Successfully assigned roles');
                } catch (error) {
                    this.logger.error(`Failed to assign roles: ${error.message}`, error);
                    // Continue even if role assignment fails
                    this.logger.warn('Continuing despite failure to assign roles');
                }
            }

            return createdUser;
        } catch (error) {
            this.logger.error(`Failed to create user in realm ${realm}`, error);
            throw new Error(`Failed to create user: ${error.message}`);
        }
    }

    /**
     * Creates a new role in a realm
     * Uses realm-specific admin token if available, otherwise falls back to master admin token
     * @param createRoleDto The role creation DTO
     * @returns The created role
     */
    async createRole(createRoleDto: CreateRoleDto): Promise<KeycloakRoleRepresentation> {
        // Get the appropriate admin token
        const adminToken = await this.getAppropriateAdminToken(
            createRoleDto.realm,
            createRoleDto.adminPassword
        );

        try {
            // Create the role
            const roleRepresentation: KeycloakRoleRepresentation = {
                name: createRoleDto.name,
                description: createRoleDto.description,
                composite: createRoleDto.composite ?? false
            };

            await this.keycloakHttp.post(
                `/admin/realms/${createRoleDto.realm}/roles`,
                roleRepresentation,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            // Get the created role
            const role = await this.keycloakHttp.get<KeycloakRoleRepresentation>(
                `/admin/realms/${createRoleDto.realm}/roles/${createRoleDto.name}`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            return role;
        } catch (error) {
            this.logger.error(`Failed to create role in realm: ${createRoleDto.realm}`, error);

            // Re-throw the error with a more specific message
            if (
                error.type === KeycloakErrorType.CLIENT &&
                error.status === HttpStatusCode.CONFLICT
            ) {
                throw new Error(
                    `Role '${createRoleDto.name}' already exists in realm '${createRoleDto.realm}'`
                );
            }

            throw new Error(`Failed to create role: ${error.message}`);
        }
    }

    /**
     * Creates a new roles in a realm from array
     * Uses realm-specific admin token if available, otherwise falls back to master admin token
     * @param createRoleDto The role creation DTO
     * @returns The created roles array
     */
    async createRoles(realm: string, adminPassword: string, roles: KeycloakRoleRepresentation[]) {
        // Get the appropriate admin token
        const adminToken = await this.getAppropriateAdminToken(realm, adminPassword);

        try {
            await Promise.all(
                roles.map((roleRepresentation) =>
                    this.keycloakHttp.post(`/admin/realms/${realm}/roles`, roleRepresentation, {
                        headers: {
                            Authorization: `Bearer ${adminToken}`
                        }
                    })
                )
            );
        } catch (error) {
            this.logger.error(`Failed to create roles in realm: ${realm}`, error);

            throw new Error(`Failed to create roles: ${error.message}`);
        }
    }

    /**
     * Assigns roles to a user
     * Uses realm-specific admin token if available, otherwise falls back to master admin token
     * @param userId The user ID to assign roles to
     * @param assignRoleDto The role assignment DTO
     */
    async assignRolesToUser(userId: string, assignRoleDto: AssignRoleDto): Promise<void> {
        // Get the appropriate admin token
        const adminToken = await this.getAppropriateAdminToken(
            assignRoleDto.realm,
            assignRoleDto.adminPassword
        );

        try {
            // Get the roles to assign
            const rolesToAssign: KeycloakRoleRepresentation[] = [];

            for (const roleName of assignRoleDto.roles) {
                try {
                    const role = await this.keycloakHttp.get<KeycloakRoleRepresentation>(
                        `/admin/realms/${assignRoleDto.realm}/roles/${roleName}`,
                        {
                            headers: {
                                Authorization: `Bearer ${adminToken}`
                            }
                        }
                    );

                    rolesToAssign.push(role);
                } catch (error) {
                    this.logger.warn(
                        `Role '${roleName}' not found in realm '${assignRoleDto.realm}' ${error?.message}`
                    );
                    // Continue with other roles
                }
            }

            if (rolesToAssign.length === 0) {
                throw new Error('None of the specified roles were found');
            }

            // Assign the roles to the user
            await this.keycloakHttp.post(
                `/admin/realms/${assignRoleDto.realm}/users/${userId}/role-mappings/realm`,
                rolesToAssign,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );
        } catch (error) {
            // If the role is already assigned, consider it a success
            if (
                error.type === KeycloakErrorType.CLIENT &&
                error.status === HttpStatusCode.CONFLICT
            ) {
                this.logger.log(`Roles are already assigned to user ${userId}`);
                return;
            }

            this.logger.error(`Failed to assign roles to user: ${userId}`, error);
            throw new Error(`Failed to assign roles: ${error.message}`);
        }
    }

    /**
     * Removes roles from a user
     * Uses realm-specific admin token if available, otherwise falls back to master admin token
     * @param userId The user ID to remove roles from
     * @param assignRoleDto The role removal DTO (reusing AssignRoleDto structure)
     */
    async removeRoleFromUser(userId: string, assignRoleDto: AssignRoleDto): Promise<void> {
        // Get the appropriate admin token
        const adminToken = await this.getAppropriateAdminToken(
            assignRoleDto.realm,
            assignRoleDto.adminPassword
        );

        try {
            // Get the roles to remove
            const rolesToRemove: KeycloakRoleRepresentation[] = [];

            for (const roleName of assignRoleDto.roles) {
                try {
                    const role = await this.keycloakHttp.get<KeycloakRoleRepresentation>(
                        `/admin/realms/${assignRoleDto.realm}/roles/${roleName}`,
                        {
                            headers: {
                                Authorization: `Bearer ${adminToken}`
                            }
                        }
                    );

                    rolesToRemove.push(role);
                } catch (error) {
                    this.logger.warn(
                        `Role '${roleName}' not found in realm '${assignRoleDto.realm}' ${error?.message}`
                    );
                    // Continue with other roles
                }
            }

            if (rolesToRemove.length === 0) {
                this.logger.warn('None of the specified roles were found for removal');
                return;
            }

            // Remove the roles from the user using DELETE method
            await this.keycloakHttp.delete(
                `/admin/realms/${assignRoleDto.realm}/users/${userId}/role-mappings/realm`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    data: rolesToRemove
                }
            );

            this.logger.log(`Successfully removed roles from user ${userId}`);
        } catch (error) {
            this.logger.error(`Failed to remove roles from user: ${userId}`, error);
            throw new Error(`Failed to remove roles: ${error.message}`);
        }
    }

    /**
     * Gets a user by username
     * Uses realm-specific admin token if available, otherwise falls back to master admin token
     * @param realm The realm to search in
     * @param username The username to search for
     * @param adminPassword Optional admin password for realm-specific admin token
     * @returns The user representation or null if not found
     */
    async getUserByUsername(
        realm: string,
        username: string,
        adminPassword?: string
    ): Promise<KeycloakUserRepresentation | null> {
        // Get the appropriate admin token
        const adminToken = await this.getAppropriateAdminToken(realm, adminPassword);

        try {
            const users = await this.keycloakHttp.get<KeycloakUserRepresentation[]>(
                `/admin/realms/${realm}/users?username=${username}&exact=true`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            if (!users || users.length === 0) {
                return null;
            }

            return users[0];
        } catch (error) {
            this.logger.error(`Failed to get user by username: ${username}`, error);
            throw new Error(`Failed to get user: ${error.message}`);
        }
    }

    /**
     * Gets a user by ID
     * Uses realm-specific admin token if available, otherwise falls back to master admin token
     * @param realm The realm to search in
     * @param userId The user ID to search for
     * @param adminPassword Optional admin password for realm-specific admin token
     * @returns The user representation
     */
    async getUserById(
        realm: string,
        userId: string,
        adminPassword?: string
    ): Promise<KeycloakUserRepresentation> {
        // Get the appropriate admin token
        const adminToken = await this.getAppropriateAdminToken(realm, adminPassword);

        try {
            return await this.keycloakHttp.get<KeycloakUserRepresentation>(
                `/admin/realms/${realm}/users/${userId}`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );
        } catch (error) {
            this.logger.error(`Failed to get user by ID: ${userId}`, error);
            throw new Error(`Failed to get user: ${error.message}`);
        }
    }

    /**
     * Gets the OpenID configuration for a realm
     */
    async getOpenIdConfiguration(realm: string): Promise<OpenIdConfiguration> {
        try {
            this.logger.debug(`Getting OpenID configuration for realm: ${realm}`);
            const url = `/realms/${realm}/.well-known/openid-configuration`;
            this.logger.debug(`OpenID configuration URL: ${url}`);
            return await this.keycloakHttp.get<OpenIdConfiguration>(url);
        } catch (error) {
            this.logger.error(`Failed to get OpenID configuration for realm: ${realm}`, error);
            throw new Error(`Failed to get OpenID configuration: ${error.message}`);
        }
    }

    /**
     * Gets the JWKS for a realm
     */
    async getJwks(realm: string): Promise<JwksResponse> {
        try {
            // First get the OpenID configuration to find the JWKS URI
            const openIdConfig = await this.getOpenIdConfiguration(realm);

            // Extract the JWKS URI path from the full URL
            const jwksUriPath = new URL(openIdConfig.jwks_uri).pathname;

            // Get the JWKS
            return await this.keycloakHttp.get<JwksResponse>(jwksUriPath);
        } catch (error) {
            this.logger.error(`Failed to get JWKS for realm: ${realm}`, error);
            throw new Error(`Failed to get JWKS: ${error.message}`);
        }
    }

    /**
     * Gets a token for a user
     */
    async getToken(
        realm: string,
        username: string,
        password: string,
        clientId: string,
        clientSecret?: string
    ): Promise<KeycloakTokenResponse> {
        try {
            this.logger.log(
                `Attempting to get token for user ${username} in realm ${realm} with client ${clientId}`
            );

            // Create form data for the token request
            const formData = new URLSearchParams({
                grant_type: 'password',
                client_id: clientId,
                username,
                password,
                scope: 'openid offline_access'
            });

            // Only add client_secret if it's provided (for confidential clients)
            if (clientSecret) {
                formData.append('client_secret', clientSecret);
                this.logger.log(`Using client secret for token request`);
            } else {
                this.logger.log(`No client secret provided, assuming public client`);
            }

            // Log the complete request for debugging
            this.logger.debug(`Token request payload: ${formData.toString()}`);
            this.logger.debug(`Token endpoint: /realms/${realm}/protocol/openid-connect/token`);

            const response = await this.keycloakHttp.post<KeycloakTokenResponse>(
                `/realms/${realm}/protocol/openid-connect/token`,
                formData
            );

            this.logger.log(`Token request successful for user ${username}`);
            return response;
        } catch (error) {
            // Log detailed error information
            this.logger.error(`Failed to get token for user ${username} in realm ${realm}`, error);

            if (error.response) {
                this.logger.error(`Error response status: ${error.response.status}`);
                this.logger.error(`Error response data: ${JSON.stringify(error.response.data)}`);

                // Check for specific error types with more detailed messages
                if (error.response.data) {
                    const errorData = error.response.data;

                    if (errorData.error === 'invalid_grant') {
                        if (errorData.error_description?.includes('Invalid user credentials')) {
                            throw new UnauthorizedException('Invalid username or password');
                        } else if (
                            errorData.error_description?.includes('Account is not fully set up')
                        ) {
                            throw new UnauthorizedException(
                                'User account requires additional setup'
                            );
                        } else if (errorData.error_description?.includes('Account is not active')) {
                            throw new UnauthorizedException('User account is disabled');
                        } else if (errorData.error_description?.includes('Invalid refresh token')) {
                            throw new UnauthorizedException('Invalid or expired refresh token');
                        } else {
                            throw new UnauthorizedException(
                                `Authentication failed: ${errorData.error_description}`
                            );
                        }
                    } else if (errorData.error === 'invalid_client') {
                        if (errorData.error_description?.includes('Client not found')) {
                            throw new UnauthorizedException(
                                `Client '${clientId}' not found in realm '${realm}'`
                            );
                        } else if (errorData.error_description?.includes('Invalid client secret')) {
                            throw new UnauthorizedException('Invalid client secret');
                        } else if (errorData.error_description?.includes('unauthorized_client')) {
                            throw new UnauthorizedException(
                                `Client '${clientId}' is not authorized for direct access grants`
                            );
                        } else {
                            throw new UnauthorizedException(
                                `Client authentication failed: ${errorData.error_description}`
                            );
                        }
                    } else if (errorData.error === 'unauthorized_client') {
                        throw new UnauthorizedException(
                            `Client '${clientId}' is not authorized for the requested grant type`
                        );
                    }
                }
            }

            // Generic error if we can't determine a specific cause
            throw new UnauthorizedException(
                'Authentication failed. Please check client configuration and user credentials.'
            );
        }
    }

    /**
     * Refreshes a token
     */
    async refreshToken(
        realm: string,
        refreshToken: string,
        clientId: string,
        clientSecret?: string
    ): Promise<KeycloakTokenResponse> {
        try {
            this.logger.log(`Refreshing token in realm ${realm} with client ${clientId}`);

            const tokenEndpoint = `/realms/${realm}/protocol/openid-connect/token`;
            const formData = new URLSearchParams({
                grant_type: 'refresh_token',
                client_id: clientId,
                refresh_token: refreshToken
            });

            // Only add client_secret if it's provided (for confidential clients)
            if (clientSecret) {
                formData.append('client_secret', clientSecret);
                this.logger.log(`Using client secret for token refresh`);
            } else {
                this.logger.log(`No client secret provided, assuming public client`);
            }

            this.logger.log(`Making refresh token request to ${tokenEndpoint}`);
            const response = await this.keycloakHttp.post<KeycloakTokenResponse>(
                tokenEndpoint,
                formData
            );

            this.logger.log(`Token refresh successful`);
            return response;
        } catch (error) {
            this.logger.error('Failed to refresh token', error);

            if (error.response) {
                this.logger.error(`Error response: ${JSON.stringify(error.response.data)}`);
            }

            throw new Error(`Failed to refresh token: ${error.message}`);
        }
    }

    /**
     * Gets a token using client credentials (service account)
     * This is used for clients with service accounts enabled
     */
    async getClientToken(
        realm: string,
        clientId: string,
        clientSecret: string
    ): Promise<KeycloakTokenResponse> {
        try {
            const tokenEndpoint = `/realms/${realm}/protocol/openid-connect/token`;
            const tokenRequest: KeycloakTokenRequest = {
                grant_type: 'client_credentials',
                client_id: clientId,
                client_secret: clientSecret
            };

            // Convert to form data
            const formData = new URLSearchParams();
            Object.entries(tokenRequest).forEach(([key, value]) => {
                if (value !== undefined) {
                    formData.append(key, value);
                }
            });

            return await this.keycloakHttp.post<KeycloakTokenResponse>(
                tokenEndpoint,
                formData.toString(),
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }
            );
        } catch (error) {
            this.logger.error(`Failed to get client token for client: ${clientId}`, error);
            throw new Error(`Failed to get client token: ${error.message}`);
        }
    }

    /**
     * Checks and fixes client configuration for direct access grants
     * @param realm The realm containing the client
     * @param clientId The client ID to check and fix
     * @returns The updated client configuration
     */
    async checkAndFixClientConfiguration(
        realm: string,
        clientId: string
    ): Promise<KeycloakClientRepresentation> {
        try {
            // Get admin token
            const adminToken = await this.getAdminToken();

            // Get the client
            const clients = await this.keycloakHttp.get<KeycloakClientRepresentation[]>(
                `/admin/realms/${realm}/clients?clientId=${clientId}&exact=true`,
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );

            if (!clients || clients.length === 0) {
                throw new Error(`Client '${clientId}' not found in realm '${realm}'`);
            }

            const client = clients[0];
            this.logger.log(`Found client: ${client.clientId} (ID: ${client.id})`);
            this.logger.log(
                `Current configuration: directAccessGrantsEnabled=${client.directAccessGrantsEnabled}, publicClient=${client.publicClient}`
            );

            // Check if configuration needs to be updated
            const needsUpdate =
                client.directAccessGrantsEnabled !== true ||
                client.publicClient !== true ||
                !client.standardFlowEnabled;

            if (needsUpdate) {
                this.logger.log(`Client '${clientId}' needs configuration update`);

                // Update client configuration
                const updatedConfig = {
                    ...client,
                    directAccessGrantsEnabled: true,
                    publicClient: true,
                    standardFlowEnabled: true,
                    implicitFlowEnabled: false,
                    fullScopeAllowed: true,
                    redirectUris: client.redirectUris || ['*'],
                    webOrigins: client.webOrigins || ['*']
                };

                await this.keycloakHttp.put(
                    `/admin/realms/${realm}/clients/${client.id}`,
                    updatedConfig,
                    { headers: { Authorization: `Bearer ${adminToken}` } }
                );

                this.logger.log(`Client '${clientId}' configuration updated`);

                // Get the updated client
                const updatedClient = await this.keycloakHttp.get<KeycloakClientRepresentation>(
                    `/admin/realms/${realm}/clients/${client.id}`,
                    { headers: { Authorization: `Bearer ${adminToken}` } }
                );

                this.logger.log(
                    `Updated configuration: directAccessGrantsEnabled=${updatedClient.directAccessGrantsEnabled}, publicClient=${updatedClient.publicClient}`
                );
                return updatedClient;
            }

            this.logger.log(`Client '${clientId}' configuration is already correct`);
            return client;
        } catch (error) {
            this.logger.error(`Failed to check/fix client configuration: ${error.message}`, error);
            throw new Error(`Failed to check/fix client configuration: ${error.message}`);
        }
    }

    /**
     * Creates a user and retrieves the user ID from the location header
     * @param realm The realm to create the user in
     * @param userRepresentation The user representation to create
     * @param adminToken The admin token for authentication
     * @returns The created user representation
     */
    private async createUserAndGetId(
        realm: string,
        userRepresentation: KeycloakUserRepresentation,
        adminToken: string
    ): Promise<KeycloakUserRepresentation> {
        try {
            // Use our method to get access to the response headers
            const response = await this.keycloakHttp.postWithFullResponse(
                `/admin/realms/${realm}/users`,
                userRepresentation,
                {
                    headers: { Authorization: `Bearer ${adminToken}` },
                    maxRedirects: 0 // Don't follow redirects
                }
            );

            // Check if we have a location header (should contain the URL to the new user)
            const locationHeader = response.headers?.location;
            this.logger.log(
                `User creation response status: ${response.status}, location: ${locationHeader}`
            );

            if (!locationHeader) {
                throw new Error('No location header in response');
            }

            // Extract the user ID from the location header
            // Location header format: /admin/realms/{realm}/users/{userId}
            const userId = locationHeader.split('/').pop();

            if (!userId) {
                throw new Error('Could not extract user ID from location header');
            }

            this.logger.log(`Extracted user ID from location header: ${userId}`);

            // Get the full user details
            const user = await this.keycloakHttp.get<KeycloakUserRepresentation>(
                `/admin/realms/${realm}/users/${userId}`,
                { headers: { Authorization: `Bearer ${adminToken}` } }
            );

            if (!user || !user.id) {
                throw new Error(`Failed to get user details for ID: ${userId}`);
            }

            this.logger.log(`Found created user with ID: ${user.id}`);
            return user;
        } catch (error) {
            this.logger.error(`Error creating or retrieving user: ${error.message}`, error);
            throw new Error(`Failed to create or retrieving user: ${error.message}`);
        }
    }

    /**
     * Clears expired tokens from the cache
     * @returns Number of tokens cleared
     */
    cleanupTokenCache(): number {
        const now = Math.floor(Date.now() / 1000);
        let clearedCount = 0;

        for (const [key, entry] of this.tokenCache.entries()) {
            // If token is expired, remove it from cache
            if (entry.expiry < now) {
                this.tokenCache.delete(key);
                clearedCount++;
            }
        }

        this.logger.log(`Cleaned up ${clearedCount} expired tokens from cache`);
        return clearedCount;
    }

    /**
     * Invalidates a specific token in the cache
     * Useful when a token becomes invalid before its expiration
     * @param realm The realm of the token to invalidate
     * @param username Optional username to invalidate only tokens for a specific user
     */
    invalidateTokenCache(realm: string, username?: string): void {
        const keysToRemove: string[] = [];

        for (const key of this.tokenCache.keys()) {
            // Key format: 'admin:realm:username:clientId' or 'admin:realm'
            if (key.includes(`:${realm}:`)) {
                // If username is specified, only invalidate tokens for that user
                if (username) {
                    if (key.includes(`:${username}:`)) {
                        keysToRemove.push(key);
                    }
                } else {
                    keysToRemove.push(key);
                }
            }
        }

        for (const key of keysToRemove) {
            this.tokenCache.delete(key);
        }

        this.logger.log(
            `Invalidated ${keysToRemove.length} tokens for realm ${realm}${username ? ` and user ${username}` : ''}`
        );
    }

    /**
     * Schedule periodic cleanup of token cache
     * Should be called during service initialization
     */
    scheduleTokenCacheCleanup(intervalMs: number = 3600000): void {
        // Default: 1 hour
        this.logger.log(`Scheduling token cache cleanup every ${intervalMs}ms`);

        setInterval(() => {
            this.logger.debug('Running scheduled token cache cleanup');
            const clearedCount = this.cleanupTokenCache();
            this.logger.debug(`Scheduled cleanup removed ${clearedCount} expired tokens`);
        }, intervalMs);
    }

    /**
     * Gets metrics about the token cache
     * Useful for monitoring and debugging
     */
    getTokenCacheMetrics(): {
        totalCachedTokens: number;
        expiredTokens: number;
        validTokens: number;
        expiringTokens: number; // Tokens expiring in the next 5 minutes
    } {
        const now = Math.floor(Date.now() / 1000);
        let expiredCount = 0;
        let expiringCount = 0;

        for (const entry of this.tokenCache.values()) {
            if (entry.expiry < now) {
                expiredCount++;
            } else if (entry.expiry < now + 300) {
                // 5 minutes
                expiringCount++;
            }
        }

        const totalCount = this.tokenCache.size;

        return {
            totalCachedTokens: totalCount,
            expiredTokens: expiredCount,
            validTokens: totalCount - expiredCount,
            expiringTokens: expiringCount
        };
    }

    /**
     * Check if Keycloak health status
     * @returns Health status information
     */
    async checkHealth(): Promise<{ status: string; details?: any }> {
        try {
            // First check the HTTP service health
            const httpHealth = await this.keycloakHttp.checkHealth();

            if (httpHealth.status !== 'UP') {
                return httpHealth;
            }

            // Try to get an admin token as a deeper check
            try {
                await this.getAdminToken();

                return {
                    status: 'UP',
                    details: {
                        message: 'Keycloak server is healthy and admin authentication is working',
                        circuitState: httpHealth.details?.circuitState,
                        serverUrl: this.authConfig.serverUrl
                    }
                };
            } catch (authError) {
                // Server is up but authentication failed
                return {
                    status: 'DEGRADED',
                    details: {
                        message: 'Keycloak server is reachable but admin authentication failed',
                        error: authError.message,
                        serverUrl: this.authConfig.serverUrl
                    }
                };
            }
        } catch (error) {
            return {
                status: 'DOWN',
                details: {
                    message: 'Failed to check Keycloak health',
                    error: error.message
                }
            };
        }
    }

    /**
     * Validates a user's credentials without actually creating a full token
     * Useful for checking if credentials are valid without side effects
     * @param realm The realm to check in
     * @param username The username to check
     * @param password The password to check
     * @returns True if credentials are valid
     */
    async validateCredentials(realm: string, username: string, password: string): Promise<boolean> {
        try {
            // Get the client to use
            const credentials = this.realmAdminCredentials.get(realm);
            const clientId = credentials?.clientId || `${realm}-admin-client`;

            // Get a token to validate credentials
            await this.keycloakHttp.post(
                `/realms/${realm}/protocol/openid-connect/token`,
                new URLSearchParams({
                    grant_type: 'password',
                    client_id: clientId,
                    username,
                    password,
                    scope: 'openid'
                })
            );

            // If we get here, credentials are valid
            return true;
        } catch (error) {
            this.logger.debug(
                `Credential validation failed for user ${username} in realm ${realm} ${error?.message}`
            );
            return false;
        }
    }

    /**
     * Deletes a realm
     * @param realm The realm name to delete
     * @returns True if successfully deleted
     */
    async deleteRealm(realm: string): Promise<boolean> {
        try {
            // For realm deletion, we need the master admin token
            const adminToken = await this.getAdminToken();

            this.logger.log(`Attempting to delete realm: ${realm}`);

            await this.keycloakHttp.delete(`/admin/realms/${realm}`, {
                headers: { Authorization: `Bearer ${adminToken}` }
            });

            // Verify the realm was deleted by checking if it exists
            try {
                await this.verifyRealmExists(realm, adminToken);
                // If we get here, the realm still exists
                this.logger.warn(`Failed to delete realm: ${realm} - realm still exists`);
                return false;
            } catch (error) {
                // If verification fails with a 404, the realm was successfully deleted
                this.logger.log(`Successfully deleted realm: ${realm}`, error);

                // Remove any cached tokens for this realm
                this.invalidateTokenCache(realm);

                return true;
            }
        } catch (error) {
            this.logger.error(`Error deleting realm: ${realm}`, error);
            return false;
        }
    }

    /**
     * Finds users by email in a realm
     * Uses realm-specific admin token if available, otherwise falls back to master admin token
     * @param realm The realm to search in
     * @param email The email to search for
     * @param adminPassword Optional admin password for realm-specific admin token
     * @returns Array of user representations matching the email
     */
    async findUsersByEmail(
        realm: string,
        email: string,
        adminPassword?: string
    ): Promise<KeycloakUserRepresentation[]> {
        // Get the appropriate admin token
        const adminToken = await this.getAppropriateAdminToken(realm, adminPassword);

        try {
            const users = await this.keycloakHttp.get<KeycloakUserRepresentation[]>(
                `/admin/realms/${realm}/users?email=${encodeURIComponent(email)}&exact=true`,
                {
                    headers: {
                        Authorization: `Bearer ${adminToken}`
                    }
                }
            );

            this.logger.log(
                `Found ${users?.length || 0} users with email ${email} in realm ${realm}`
            );
            return users || [];
        } catch (error) {
            this.logger.error(`Failed to find users by email: ${email}`, error);
            throw new Error(`Failed to find users by email: ${error.message}`);
        }
    }
}
