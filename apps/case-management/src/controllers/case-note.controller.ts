import { Body, Controller, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { CaseNoteService } from '../services/case-note.service';
import { CreateNoteDto } from '../dto/create-note.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroup,
    AllowRoleGroups
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';

@Controller('cases/:caseId/notes')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard)
export class CaseNoteController {
    constructor(private readonly caseNoteService: CaseNoteService) {}

    /**
     * Create a new note for a case
     * Requires CREATE permission on CASE resource and access to conveyancers role group
     */
    @Post()
    @HasPermission(ResourceType.CASE, Permission.CREATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async createNote(
        @Param('caseId') caseId: string,
        @Body() createNoteDto: CreateNoteDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const note = await this.caseNoteService.createNote(
            caseId,
            createNoteDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(note, 'Note created successfully');
    }

    /**
     * Get all notes for a case
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get()
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getCaseNotes(
        @Param('caseId') caseId: string,
        @Query('includePrivate') includePrivate: boolean = false
    ) {
        const notes = await this.caseNoteService.getCaseNotes(caseId, includePrivate);
        return ApiResponseUtil.ok(notes, 'Notes retrieved successfully');
    }

    /**
     * Get a specific note by ID
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get(':noteId')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getNoteById(@Param('noteId') noteId: string) {
        const note = await this.caseNoteService.getNoteById(noteId);
        return ApiResponseUtil.ok(note, 'Note retrieved successfully');
    }

    /**
     * Toggle the pin status of a note
     * Requires UPDATE permission on CASE resource and access to conveyancers role group
     */
    @Patch(':noteId/pin')
    @HasPermission(ResourceType.CASE, Permission.UPDATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async togglePinStatus(@Param('noteId') noteId: string, @Body('isPinned') isPinned: boolean) {
        await this.caseNoteService.togglePinStatus(noteId, isPinned);
        return ApiResponseUtil.ok(null, `Note ${isPinned ? 'pinned' : 'unpinned'} successfully`);
    }

    /**
     * Get all pinned notes for a case
     * Returns important notes flagged as priority
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get('pinned')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getPinnedNotes(@Param('caseId') caseId: string) {
        const notes = await this.caseNoteService.getPinnedNotes(caseId);
        return ApiResponseUtil.ok(notes, 'Pinned notes retrieved successfully');
    }

    /**
     * Get recent notes for a case
     * Returns the 3 most recent notes
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get('recent')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getRecentNotes(@Param('caseId') caseId: string) {
        const notes = await this.caseNoteService.getRecentNotes(caseId, 3);
        return ApiResponseUtil.ok(notes, 'Recent notes retrieved successfully');
    }
}
