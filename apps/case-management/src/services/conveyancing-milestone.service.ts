import { Injectable, Logger } from '@nestjs/common';
import { EnhancedMilestoneService } from '@app/common/services/enhanced-milestone.service';
import { CaseType } from '@app/common/typeorm/entities/tenant/case.entity';

/**
 * Conveyancing-specific milestone service
 * Delegates to EnhancedMilestoneService for actual implementation
 * Maintains backward compatibility while using centralized logic
 */
@Injectable()
export class ConveyancingMilestoneService {
    private readonly logger = new Logger(ConveyancingMilestoneService.name);

    constructor(
        private readonly enhancedMilestoneService: EnhancedMilestoneService
    ) {}

    /**
     * Create default milestones and tasks for a new conveyancing case
     * Delegates to EnhancedMilestoneService for implementation
     */
    async createDefaultMilestonesForCase(caseId: string, createdBy: string): Promise<void> {
        this.logger.log(`Creating default conveyancing milestones for case ${caseId}`);

        await this.enhancedMilestoneService.createDefaultMilestonesForCase(
            caseId,
            CaseType.REAL_ESTATE,
            createdBy
        );

        this.logger.log(`Successfully created default milestones for conveyancing case ${caseId}`);
    }

    /**
     * Get milestones with progress tracking for a case
     * Delegates to EnhancedMilestoneService for implementation
     */
    async getMilestonesWithProgress(caseId: string): Promise<any[]> {
        return await this.enhancedMilestoneService.getMilestonesWithProgress(caseId);
    }

    /**
     * Update milestone progress when a task status changes
     * Delegates to EnhancedMilestoneService for implementation
     */
    async updateMilestoneProgress(milestoneId: string): Promise<void> {
        await this.enhancedMilestoneService.updateMilestoneProgress(milestoneId);
    }
}
                    {
                        title: 'Review client questionnaire responses',
                        description: 'Analyze client responses about property transaction details',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 1,
                        isDefault: true
                    },
                    {
                        title: 'Verify property details',
                        description: 'Confirm property address, title, and basic details',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 1,
                        isDefault: true
                    },
                    {
                        title: 'Send starter pack to client',
                        description: 'Email client with initial documents to sign',
                        priority: TaskPriority.HIGHEST,
                        estimatedDays: 0,
                        isDefault: true
                    }
                ]
            },
            {
                name: 'Property Searches & Investigations',
                description: 'Conduct necessary property searches and investigations',
                sortOrder: 2,
                targetDays: 14,
                tasks: [
                    {
                        title: 'Order local authority searches',
                        description: 'Request local authority search reports',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 1,
                        isDefault: true
                    },
                    {
                        title: 'Order environmental searches',
                        description: 'Request environmental and contamination reports',
                        priority: TaskPriority.MEDIUM,
                        estimatedDays: 1,
                        isDefault: true
                    },
                    {
                        title: 'Review search results',
                        description: 'Analyze search results and identify potential issues',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 2,
                        isDefault: true
                    },
                    {
                        title: 'Raise enquiries with seller solicitor',
                        description: "Submit property enquiries to seller's solicitor",
                        priority: TaskPriority.MEDIUM,
                        estimatedDays: 1,
                        isDefault: true
                    }
                ]
            },
            {
                name: 'Contract Review & Negotiation',
                description: 'Review draft contract and negotiate terms',
                sortOrder: 3,
                targetDays: 21,
                tasks: [
                    {
                        title: 'Review draft contract',
                        description: 'Examine draft purchase/sale contract for issues',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 2,
                        isDefault: true
                    },
                    {
                        title: 'Advise client on contract terms',
                        description: 'Explain contract terms and conditions to client',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 1,
                        isDefault: true
                    },
                    {
                        title: 'Negotiate contract amendments',
                        description: 'Negotiate required changes with other party',
                        priority: TaskPriority.MEDIUM,
                        estimatedDays: 3,
                        isDefault: true
                    }
                ]
            },
            {
                name: 'Exchange of Contracts',
                description: 'Finalize and exchange contracts with completion date',
                sortOrder: 4,
                targetDays: 28,
                tasks: [
                    {
                        title: 'Obtain client signatures',
                        description: 'Get client to sign contract and obtain deposit',
                        priority: TaskPriority.HIGHEST,
                        estimatedDays: 1,
                        isDefault: true
                    },
                    {
                        title: 'Coordinate contract exchange',
                        description: "Exchange contracts with other party's solicitor",
                        priority: TaskPriority.HIGHEST,
                        estimatedDays: 0,
                        isDefault: true
                    },
                    {
                        title: 'Confirm completion date',
                        description: 'Agree and confirm completion date with all parties',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 0,
                        isDefault: true
                    }
                ]
            },
            {
                name: 'Pre-Completion Preparations',
                description: 'Complete all pre-completion requirements',
                sortOrder: 5,
                targetDays: 35,
                tasks: [
                    {
                        title: 'Prepare completion statement',
                        description: 'Calculate final completion monies required',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 1,
                        isDefault: true
                    },
                    {
                        title: 'Arrange mortgage funds',
                        description: 'Request mortgage advance from lender',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 2,
                        isDefault: true
                    },
                    {
                        title: 'Prepare transfer deed',
                        description: 'Draft transfer deed for signature',
                        priority: TaskPriority.MEDIUM,
                        estimatedDays: 1,
                        isDefault: true
                    }
                ]
            },
            {
                name: 'Completion & Post-Completion',
                description: 'Complete transaction and handle post-completion matters',
                sortOrder: 6,
                targetDays: 42,
                tasks: [
                    {
                        title: 'Complete transaction',
                        description: 'Exchange completion monies and receive keys/deeds',
                        priority: TaskPriority.HIGHEST,
                        estimatedDays: 0,
                        isDefault: true
                    },
                    {
                        title: 'Register title at Land Registry',
                        description: 'Submit application to register new ownership',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 3,
                        isDefault: true
                    },
                    {
                        title: 'Pay Stamp Duty Land Tax',
                        description: 'Submit SDLT return and payment to HMRC',
                        priority: TaskPriority.HIGH,
                        estimatedDays: 7,
                        isDefault: true
                    },
                    {
                        title: 'Send completion report to client',
                        description: 'Provide final report and return client documents',
                        priority: TaskPriority.MEDIUM,
                        estimatedDays: 5,
                        isDefault: true
                    }
                ]
            }
        ];
    }

    /**
     * Create default milestones and tasks for a new conveyancing case
     */
    async createDefaultMilestonesForCase(caseId: string, createdBy: string): Promise<void> {
        const defaultConfig = this.getDefaultMilestonesConfig();

        this.logger.log(`Creating default milestones for case ${caseId}`);

        for (const milestoneConfig of defaultConfig) {
            // Create milestone
            const milestone = this.milestoneRepository.create({
                name: milestoneConfig.name,
                description: milestoneConfig.description,
                caseId,
                sortOrder: milestoneConfig.sortOrder,
                status: MilestoneStatus.PENDING,
                isDefault: true,
                targetDate: milestoneConfig.targetDays
                    ? new Date(Date.now() + milestoneConfig.targetDays * 24 * 60 * 60 * 1000)
                    : null,
                createdBy
            });

            const savedMilestone = await this.milestoneRepository.save(milestone);

            // Create tasks for this milestone
            for (const taskConfig of milestoneConfig.tasks) {
                const task = this.taskRepository.create({
                    title: taskConfig.title,
                    description: taskConfig.description,
                    priority: taskConfig.priority,
                    status: TaskStatus.OPEN,
                    caseId,
                    milestoneId: savedMilestone.id,
                    isDefault: taskConfig.isDefault,
                    dueDate: taskConfig.estimatedDays
                        ? new Date(Date.now() + taskConfig.estimatedDays * 24 * 60 * 60 * 1000)
                        : null,
                    createdBy
                });

                await this.taskRepository.save(task);
            }

            this.logger.log(
                `Created milestone '${milestone.name}' with ${milestoneConfig.tasks.length} tasks`
            );
        }

        this.logger.log(
            `Successfully created ${defaultConfig.length} default milestones for case ${caseId}`
        );
    }

    /**
     * Get milestones with progress tracking for a case
     */
    async getMilestonesWithProgress(caseId: string): Promise<any[]> {
        const milestones = await this.milestoneRepository.find({
            where: { caseId },
            relations: ['tasks'],
            order: { sortOrder: 'ASC' }
        });

        return milestones.map((milestone) => {
            const totalTasks = milestone.tasks.length;
            const completedTasks = milestone.tasks.filter(
                (task) => task.status === TaskStatus.DONE
            ).length;
            const inProgressTasks = milestone.tasks.filter(
                (task) => task.status === TaskStatus.IN_PROGRESS
            ).length;

            const progressPercentage =
                totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

            // Update milestone status based on task completion
            let status = MilestoneStatus.PENDING;
            if (completedTasks === totalTasks && totalTasks > 0) {
                status = MilestoneStatus.COMPLETED;
            } else if (completedTasks > 0 || inProgressTasks > 0) {
                status = MilestoneStatus.IN_PROGRESS;
            }

            return {
                ...milestone,
                progressPercentage,
                status,
                taskSummary: {
                    total: totalTasks,
                    completed: completedTasks,
                    inProgress: inProgressTasks,
                    pending: totalTasks - completedTasks - inProgressTasks
                }
            };
        });
    }

    /**
     * Update milestone progress when a task status changes
     */
    async updateMilestoneProgress(milestoneId: string): Promise<void> {
        const milestone = await this.milestoneRepository.findOne({
            where: { id: milestoneId },
            relations: ['tasks']
        });

        if (!milestone) {
            this.logger.warn(`Milestone ${milestoneId} not found for progress update`);
            return;
        }

        const totalTasks = milestone.tasks.length;
        const completedTasks = milestone.tasks.filter(
            (task) => task.status === TaskStatus.DONE
        ).length;
        const inProgressTasks = milestone.tasks.filter(
            (task) => task.status === TaskStatus.IN_PROGRESS
        ).length;

        // Calculate progress percentage
        const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

        // Determine status
        let status = MilestoneStatus.PENDING;
        let completionDate: Date | null = null;

        if (completedTasks === totalTasks && totalTasks > 0) {
            status = MilestoneStatus.COMPLETED;
            completionDate = new Date();
        } else if (completedTasks > 0 || inProgressTasks > 0) {
            status = MilestoneStatus.IN_PROGRESS;
        }

        // Update milestone
        milestone.progressPercentage = Math.round(progressPercentage * 100) / 100; // Round to 2 decimal places
        milestone.status = status;
        milestone.completionDate = completionDate;
        milestone.updatedAt = new Date();

        await this.milestoneRepository.save(milestone);

        this.logger.log(
            `Updated milestone ${milestoneId} progress: ${progressPercentage.toFixed(1)}% (${completedTasks}/${totalTasks} tasks completed)`
        );
    }
}
