import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

@Injectable()
export class DocumentLinkService {
    private readonly logger = new Logger(DocumentLinkService.name);

    constructor(private readonly configService: ConfigService) {}

    /**
     * Generate a secure document link with verification parameters
     */
    generateSecureDocumentLink(clientId: string, caseId: string, propertyType: string): string {
        // Generate a secure token
        const tokenData = {
            clientId,
            caseId,
            propertyType,
            timestamp: Date.now(),
            // Add random salt to prevent prediction
            salt: crypto.randomBytes(16).toString('hex')
        };

        // Create verification token
        const token = this.createVerificationToken(tokenData);

        // Build the secure URL
        const baseUrl =
            this.configService.get('DOCUMENT_PORTAL_URL') || 'https://portal.yourcompany.com';
        const documentUrl = `${baseUrl}/documents/starter-pack?clientId=${clientId}&caseId=${caseId}&token=${token}`;

        this.logger.log(`Generated secure document link for client ${clientId}, case ${caseId}`);

        return documentUrl;
    }

    /**
     * Create a secure verification token
     */
    private createVerificationToken(data: any): string {
        const secret = this.configService.get('DOCUMENT_LINK_SECRET') || 'default-secret-key';
        const payload = JSON.stringify(data);

        // Create HMAC signature
        const hmac = crypto.createHmac('sha256', secret);
        hmac.update(payload);
        const signature = hmac.digest('hex');

        // Combine payload and signature
        const token = Buffer.from(
            JSON.stringify({
                payload: Buffer.from(payload).toString('base64'),
                signature
            })
        ).toString('base64');

        return token;
    }

    /**
     * Verify and decode a document link token
     */
    verifyDocumentToken(token: string): { valid: boolean; data?: any; error?: string } {
        try {
            const decoded = JSON.parse(Buffer.from(token, 'base64').toString());
            const payload = Buffer.from(decoded.payload, 'base64').toString();
            const signature = decoded.signature;

            // Verify signature
            const secret = this.configService.get('DOCUMENT_LINK_SECRET') || 'default-secret-key';
            const hmac = crypto.createHmac('sha256', secret);
            hmac.update(payload);
            const expectedSignature = hmac.digest('hex');

            if (signature !== expectedSignature) {
                return { valid: false, error: 'Invalid token signature' };
            }

            const data = JSON.parse(payload);

            // Check token age (24 hours expiry)
            const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
            if (Date.now() - data.timestamp > maxAge) {
                return { valid: false, error: 'Token expired' };
            }

            return { valid: true, data };
        } catch (error) {
            this.logger.error(`Failed to verify document token: ${error.message}`);
            return { valid: false, error: 'Invalid token format' };
        }
    }

    /**
     * Generate property-specific document URLs based on questionnaire responses
     */
    generatePropertySpecificDocuments(propertyType: string, questionnaireLet: any): string[] {
        const documents: string[] = [];

        // Base documents for all property types
        documents.push('Client Care Letter');
        documents.push('Terms of Business');
        documents.push('Anti-Money Laundering Form');

        // Property type specific documents
        if (propertyType === 'BUYING') {
            documents.push('Purchase Information Form');
            documents.push('Mortgage Requirements Form');

            if (questionnaireLet?.isFirstTimeBuyer) {
                documents.push('First Time Buyer Guide');
            }

            if (questionnaireLet?.hasChain) {
                documents.push('Chain Management Information');
            }
        } else if (propertyType === 'SELLING') {
            documents.push('Property Information Form');
            documents.push('Fixtures and Fittings Form');

            if (questionnaireLet?.hasLeasehold) {
                documents.push('Leasehold Information Form');
            }
        }

        // Additional documents based on property characteristics
        if (questionnaireLet?.isNewBuild) {
            documents.push('New Build Warranty Information');
        }

        if (questionnaireLet?.hasSharedOwnership) {
            documents.push('Shared Ownership Documentation');
        }

        return documents;
    }
}
