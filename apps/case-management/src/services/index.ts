import { CaseService } from './case.service';
import { ClientService } from './client.service';
import { CaseAssignmentService } from './case-assignment.service';
import { CaseNoteService } from './case-note.service';
import { CaseAttachmentService } from './case-attachment.service';
import { CaseAuditService } from './case-audit.service';
import { CaseContactService } from './case-contact.service';
import { CaseEventService } from './case-event.service';
import { CaseRelationService } from './case-relation.service';
import { CaseNotificationService } from './case-notification.service';
import { PaginationService } from './pagination.service';
import { CaseNumberGenerator } from '../utils/case-number-generator';
import { CaseStateMachineService } from '@app/common/cases/case-state-machine.service';
import { ConveyancingMilestoneService } from './conveyancing-milestone.service';
import { DocumentLinkService } from './document-link.service';

export const CASE_MANAGEMENT_SERVICES = [
    CaseService,
    ClientService,
    CaseAssignmentService,
    CaseNoteService,
    CaseAttachmentService,
    CaseAuditService,
    CaseContactService,
    CaseEventService,
    CaseRelationService,
    CaseNotificationService,
    PaginationService,
    CaseNumberGenerator,
    CaseStateMachineService,
    ConveyancingMilestoneService,
    DocumentLinkService
];

export {
    CaseService,
    ClientService,
    CaseAssignmentService,
    CaseNoteService,
    CaseAttachmentService,
    CaseAuditService,
    CaseContactService,
    CaseEventService,
    CaseRelationService,
    CaseNotificationService,
    PaginationService,
    ConveyancingMilestoneService,
    DocumentLinkService
};
