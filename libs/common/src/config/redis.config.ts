import { registerAs } from '@nestjs/config';
import { RedisConfig, RawRedisEnv } from './interfaces/redis.config.interface';

/**
 * Validates that all required environment variables are present
 * Throws an error if any required variable is missing
 * @param env Raw environment variables
 */
function validateRedisEnv(env: RawRedisEnv): void {
    // Define required environment variables
    const requiredVars: Array<keyof RawRedisEnv> = [
        'REDIS_HOST',
        'REDIS_PORT',
        'REDIS_PASSWORD',
        'REDIS_DB'
    ];

    // Check each required variable
    for (const key of requiredVars) {
        if (env[key] === undefined) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
    }
}

/**
 * Redis configuration
 * Loads raw environment variables, validates them, and returns a fully validated config
 */
export const redisConfig = registerAs('redis', (): RedisConfig => {
    // Load raw environment variables
    const rawEnv: RawRedisEnv = {
        REDIS_HOST: process.env.REDIS_HOST,
        REDIS_PORT: process.env.REDIS_PORT,
        REDIS_USERNAME: process.env.REDIS_USERNAME,
        REDIS_PASSWORD: process.env.REDIS_PASSWORD,
        REDIS_DB: process.env.REDIS_DB,
        REDIS_TLS: process.env.REDIS_TLS
    };

    // Validate environment variables
    validateRedisEnv(rawEnv);

    return {
        host: rawEnv.REDIS_HOST!,
        port: parseInt(rawEnv.REDIS_PORT!, 10),
        username: rawEnv.REDIS_USERNAME,
        password: rawEnv.REDIS_PASSWORD!,
        db: parseInt(rawEnv.REDIS_DB!, 10),
        tls: rawEnv.REDIS_TLS === 'true'
    };
});
