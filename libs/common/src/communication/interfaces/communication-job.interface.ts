import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';

export interface JobResponse {
    jobId: string;
    queueName: string;
    estimatedDelay?: number;
    status: 'queued' | 'delayed' | 'failed';
    channels?: string[];
}

export interface ChannelResult {
    channel: string;
    status: 'fulfilled' | 'rejected';
    result?: any;
    error?: string;
}

export interface MultiChannelJobResult {
    results: ChannelResult[];
    strategy: 'fail-fast' | 'continue-on-error' | 'sequential';
    status: 'all-completed' | 'partial-success';
    successes: number;
    failures: number;
}

export interface BaseTemplateVariables {
    tenantName?: string;
    tenantId?: string;
    tenantLogo?: string;
    tenantAddress?: string;
    recipientName?: string;
    userName?: string;
    name?: string;
    supportEmail?: string;
    loginUrl?: string;
    unsubscribeUrl?: string;
    message?: string;
    currentYear?: string;
    currentDate?: string;
    type?: string;
    clickAction?: string;
}

export interface CaseTemplateVariables extends BaseTemplateVariables {
    caseId: string;
    caseNumber?: string;
    status?: string;
    caseType?: string;
    caseSummary?: string;
    description?: string;
    clientName?: string;
    deadline?: string | Date;
    openingDate?: string | Date;
    courtDate?: string | Date;
    courtTime?: string;
    assignedLawyer?: string;
    handlerName?: string;
    handlerTitle?: string;
    handlerEmail?: string;
    handlerPhone?: string;
    urgency?: 'low' | 'normal' | 'high' | 'critical';
    isUrgent?: boolean;
    nextSteps?: string | string[];
    additionalDetails?: string;
    hasMoreDetails?: boolean;
    caseUrl?: string;
}

export interface AuthTemplateVariables extends BaseTemplateVariables {
    role?: string;
    inviterName?: string;
    inviteUrl?: string;
    resetUrl?: string;
    passwordResetToken?: string;
    expirationTime?: string;
    securityTip?: string;
    invitationToken?: string;
    isWelcome?: boolean;
    welcomeMessage?: string;
    accountSetupUrl?: string;
    gettingStartedUrl?: string;
}

export interface BillingTemplateVariables extends BaseTemplateVariables {
    clientName?: string;
    amount?: number;
    totalAmount?: number;
    formattedAmount?: string;
    dueDate?: string | Date;
    formattedDueDate?: string;
    billingMonth?: string;
    invoiceNumber?: string;
    paymentUrl?: string;
    itemizedCharges?: Array<{
        description: string;
        amount: number;
        quantity?: number;
    }>;
    gracePeriod?: string;
}

export interface SystemTemplateVariables extends BaseTemplateVariables {
    maintenanceDate?: string | Date;
    formattedMaintenanceDate?: string;
    maintenanceTime?: string;
    expectedDowntime?: string;
    affectedServices?: string | string[];
    affectedServicesArray?: string[];
}

export interface ReportTemplateVariables extends BaseTemplateVariables {
    reportPeriod?: string;
    reportUrl?: string;
    newCases?: number;
    closedCases?: number;
    activeCases?: number;
    casesSummary?: {
        total: number;
        byStatus: Record<string, number>;
        byType: Record<string, number>;
    };
    metrics?: Record<string, any>;
}

export interface CaseUpdateVariables extends CaseTemplateVariables {
    type?: 'case-update';
    status: string;
}

export interface CaseUrgentVariables extends CaseTemplateVariables {
    type?: 'case-urgent';
    urgency: 'high' | 'critical';
}

export interface CaseCreatedVariables extends CaseTemplateVariables {
    type?: 'case-created';
}

export interface WelcomeVariables extends AuthTemplateVariables {
    type?: 'welcome';
    tenantName: string;
    recipientName: string;
}

export interface PasswordResetVariables extends AuthTemplateVariables {
    type?: 'password-reset';
    resetUrl: string;
    tenantName: string;
    recipientName: string;
}

export interface UserInvitationVariables extends AuthTemplateVariables {
    type?: 'user-invitation';
    inviteUrl: string;
    tenantName: string;
    recipientName: string;
}

export interface BillingStatementVariables extends BillingTemplateVariables {
    type?: 'billing-statement';
    billingMonth: string;
    formattedAmount: string;
    tenantName: string;
    clientName: string;
}

export interface PaymentReminderVariables extends BillingTemplateVariables {
    type?: 'payment-reminder';
    formattedAmount: string;
    formattedDueDate: string;
    tenantName: string;
    clientName: string;
}

export interface SystemMaintenanceVariables extends SystemTemplateVariables {
    type?: 'system-maintenance';
    formattedMaintenanceDate: string;
    tenantName: string;
    recipientName: string;
}

export interface WeeklyReportVariables extends ReportTemplateVariables {
    type?: 'weekly-report';
    reportPeriod: string;
    tenantName: string;
    recipientName: string;
}

export interface GenericNotificationVariables extends BaseTemplateVariables {
    type?: 'generic-notification';
    message: string;
    tenantName: string;
    recipientName: string;
}

export type TemplateVariables =
    | CaseUpdateVariables
    | CaseUrgentVariables
    | CaseCreatedVariables
    | WelcomeVariables
    | PasswordResetVariables
    | UserInvitationVariables
    | BillingStatementVariables
    | PaymentReminderVariables
    | SystemMaintenanceVariables
    | WeeklyReportVariables
    | GenericNotificationVariables;

export interface CommunicationJobData {
    tenantId: string;
    userId: string;
    channels: COMMUNICATION_CHANNELS[];
    recipients: {
        email?: string[];
        sms?: string[];
        notification?: string[];
    };
    variables: TemplateVariables;
    priority?: number;
    delay?: number;
    caseId?: string;
    scheduledAt?: Date;
    metadata?: Record<string, any>;
    processInParallel?: boolean;
    failureStrategy?: 'fail-fast' | 'continue-on-error';
}

export interface SingleChannelJobData {
    tenantId: string;
    userId: string;
    channel: COMMUNICATION_CHANNELS;
    recipient: string;
    variables: TemplateVariables;
    priority?: number;
    delay?: number;
    caseId?: string;
    scheduledAt?: Date;
    metadata?: Record<string, any>;
    originalJobId?: string;
}

export interface ScheduledJobData extends CommunicationJobData {
    cronExpression: string;
    timezone?: string;
    endDate?: Date;
}

// Template validation helper types
export interface TemplateRequirements {
    'case-update': {
        required: ['tenantName', 'recipientName', 'caseNumber', 'status'];
        optional: [
            'caseSummary',
            'nextSteps',
            'handlerName',
            'handlerTitle',
            'urgency',
            'deadline'
        ];
    };
    'case-urgent': {
        required: ['tenantName', 'recipientName', 'caseNumber', 'urgency'];
        optional: ['message', 'handlerName', 'courtDate', 'courtTime'];
    };
    'case-created': {
        required: ['tenantName', 'recipientName', 'caseNumber'];
        optional: ['caseType', 'assignedLawyer', 'loginUrl'];
    };
    welcome: {
        required: ['tenantName', 'recipientName'];
        optional: ['role', 'inviterName', 'loginUrl', 'assignedLawyer'];
    };
    'password-reset': {
        required: ['tenantName', 'recipientName', 'resetUrl'];
        optional: ['expirationTime', 'securityTip'];
    };
    'user-invitation': {
        required: ['tenantName', 'recipientName', 'inviteUrl'];
        optional: ['inviterName', 'role', 'loginUrl'];
    };
    'billing-statement': {
        required: ['tenantName', 'clientName', 'billingMonth', 'formattedAmount'];
        optional: ['invoiceNumber', 'formattedDueDate', 'paymentUrl', 'itemizedCharges'];
    };
    'payment-reminder': {
        required: ['tenantName', 'clientName', 'formattedAmount', 'formattedDueDate'];
        optional: ['invoiceNumber', 'paymentUrl', 'gracePeriod'];
    };
    'system-maintenance': {
        required: ['tenantName', 'recipientName', 'formattedMaintenanceDate'];
        optional: ['maintenanceTime', 'expectedDowntime', 'affectedServices'];
    };
    'weekly-report': {
        required: ['tenantName', 'recipientName', 'reportPeriod'];
        optional: ['casesSummary', 'reportUrl', 'newCases', 'closedCases', 'activeCases'];
    };
    'generic-notification': {
        required: ['tenantName', 'recipientName', 'message'];
        optional: ['loginUrl', 'supportEmail'];
    };
}

// Helper type for extracting required variables by template type
export type RequiredVariables<T extends keyof TemplateRequirements> =
    TemplateRequirements[T]['required'][number];

export type OptionalVariables<T extends keyof TemplateRequirements> =
    TemplateRequirements[T]['optional'][number];

// Type guard functions - return specific template types that are in the union
export function isCaseTemplate(
    variables: TemplateVariables
): variables is CaseUpdateVariables | CaseUrgentVariables | CaseCreatedVariables {
    return (
        variables.type === 'case-update' ||
        variables.type === 'case-urgent' ||
        variables.type === 'case-created' ||
        'caseId' in variables ||
        'caseNumber' in variables
    );
}

export function isAuthTemplate(
    variables: TemplateVariables
): variables is WelcomeVariables | PasswordResetVariables | UserInvitationVariables {
    return (
        variables.type === 'welcome' ||
        variables.type === 'password-reset' ||
        variables.type === 'user-invitation' ||
        'resetUrl' in variables ||
        'inviteUrl' in variables ||
        'isWelcome' in variables
    );
}

export function isBillingTemplate(
    variables: TemplateVariables
): variables is BillingStatementVariables | PaymentReminderVariables {
    return (
        variables.type === 'billing-statement' ||
        variables.type === 'payment-reminder' ||
        'billingMonth' in variables ||
        'invoiceNumber' in variables ||
        'amount' in variables
    );
}

export function isSystemTemplate(
    variables: TemplateVariables
): variables is SystemMaintenanceVariables {
    return (
        variables.type === 'system-maintenance' ||
        'maintenanceDate' in variables ||
        'formattedMaintenanceDate' in variables
    );
}

export function isReportTemplate(variables: TemplateVariables): variables is WeeklyReportVariables {
    return (
        variables.type === 'weekly-report' ||
        'reportPeriod' in variables ||
        'reportUrl' in variables
    );
}

// Template variable validation function with proper typing
export function validateTemplateVariables(
    templateType: keyof TemplateRequirements,
    variables: Record<string, any>
): variables is TemplateVariables {
    const requirements = {
        'case-update': ['tenantName', 'recipientName', 'caseNumber', 'status'],
        'case-urgent': ['tenantName', 'recipientName', 'caseNumber', 'urgency'],
        'case-created': ['tenantName', 'recipientName', 'caseNumber'],
        welcome: ['tenantName', 'recipientName'],
        'password-reset': ['tenantName', 'recipientName', 'resetUrl'],
        'user-invitation': ['tenantName', 'recipientName', 'inviteUrl'],
        'billing-statement': ['tenantName', 'clientName', 'billingMonth', 'formattedAmount'],
        'payment-reminder': ['tenantName', 'clientName', 'formattedAmount', 'formattedDueDate'],
        'system-maintenance': ['tenantName', 'recipientName', 'formattedMaintenanceDate'],
        'weekly-report': ['tenantName', 'recipientName', 'reportPeriod'],
        'generic-notification': ['tenantName', 'recipientName', 'message']
    } as const;

    const required = requirements[templateType];
    const missing = required.filter(
        (field) => !variables[field] || String(variables[field]).trim() === ''
    );

    if (missing.length > 0) {
        throw new Error(`Missing required variables for ${templateType}: ${missing.join(', ')}`);
    }

    return true;
}

// Additional helper function for specific template type validation
export function validateSpecificTemplateVariables<T extends keyof TemplateRequirements>(
    templateType: T,
    variables: Record<string, any>
): asserts variables is TemplateVariables {
    validateTemplateVariables(templateType, variables);
}
