import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { Queue } from 'bullmq';
import {
    CommunicationJobData,
    SingleChannelJobData,
    JobResponse,
    ScheduledJobData,
    TemplateRequirements
} from '../interfaces/communication-job.interface';
import {
    getJobName,
    QUEUE_NAMES,
    QUEUE_PRIORITIES,
    QueueName
} from '@app/common/constants/queue.constants';
import { InjectQueue } from '@nestjs/bullmq';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { validateTemplateVariables } from '..';

@Injectable()
export class MessageProducerService {
    private readonly logger = new Logger(MessageProducerService.name);

    constructor(
        @InjectQueue(QUEUE_NAMES.EMAIL) private emailQueue: Queue,
        @InjectQueue(QUEUE_NAMES.NOTIFICATION) private notificationQueue: Queue,
        @InjectQueue(QUEUE_NAMES.MULTI_CHANNEL) private multiChannelQueue: Queue
    ) {}

    async enqueueMessage(jobData: CommunicationJobData): Promise<JobResponse> {
        this.validateJobData(jobData);
        this.validateTemplateVariables(jobData);

        if (jobData.channels.length > 1) {
            return await this.enqueueMultiChannelJob(jobData);
        }

        return await this.enqueueSingleChannelJob(jobData);
    }

    private async enqueueMultiChannelJob(jobData: CommunicationJobData): Promise<JobResponse> {
        const jobName = getJobName(jobData.tenantId, QUEUE_NAMES.MULTI_CHANNEL, jobData.channels);

        try {
            const job = await this.multiChannelQueue.add(jobName, jobData, {
                priority: jobData.priority || QUEUE_PRIORITIES.NORMAL,
                delay: jobData.delay || 0,
                removeOnComplete: 50,
                removeOnFail: 20,
                attempts: 3,
                backoff: {
                    type: 'exponential',
                    delay: 2000
                },
                jobId: this.generateJobId(jobData)
            });

            this.logger.log(
                `Multi-channel message queued: ${job.id} for tenant ${jobData.tenantId}, channels: ${jobData.channels.join(', ')}, template: ${this.determineTemplateType(jobData.variables)}`
            );

            return {
                jobId: job.id?.toString() || 'unknown',
                queueName: this.multiChannelQueue.name,
                estimatedDelay: jobData.delay,
                status: jobData.delay ? 'delayed' : 'queued',
                channels: jobData.channels
            };
        } catch (error) {
            this.logger.error(
                `Failed to enqueue multi-channel message: ${error.message}`,
                error.stack
            );
            return {
                jobId: '',
                queueName: this.multiChannelQueue.name,
                status: 'failed',
                channels: jobData.channels
            };
        }
    }

    private async enqueueSingleChannelJob(jobData: CommunicationJobData): Promise<JobResponse> {
        const channel = jobData.channels[0];
        const queue = this.getQueueByChannel(channel);
        const jobName = getJobName(jobData.tenantId, queue.name as QueueName);

        const singleChannelData: SingleChannelJobData = {
            tenantId: jobData.tenantId,
            userId: jobData.userId,
            channel: channel,
            recipient: this.getRecipientForChannel(jobData.recipients, channel),
            variables: jobData.variables,
            priority: jobData.priority,
            delay: jobData.delay,
            caseId: jobData.caseId,
            scheduledAt: jobData.scheduledAt,
            metadata: jobData.metadata
        };

        try {
            const job = await queue.add(jobName, singleChannelData, {
                priority: jobData.priority || QUEUE_PRIORITIES.NORMAL,
                delay: jobData.delay || 0,
                removeOnComplete: 50,
                removeOnFail: 20,
                attempts: 3,
                backoff: {
                    type: 'exponential',
                    delay: 2000
                },
                jobId: this.generateJobId(jobData)
            });

            this.logger.log(
                `Single-channel message queued: ${job.id} for tenant ${jobData.tenantId}, channel: ${channel}, template: ${this.determineTemplateType(jobData.variables)}`
            );

            return {
                jobId: job.id?.toString() || 'unknown',
                queueName: queue.name,
                estimatedDelay: jobData.delay,
                status: jobData.delay ? 'delayed' : 'queued',
                channels: [channel]
            };
        } catch (error) {
            this.logger.error(
                `Failed to enqueue single-channel message: ${error.message}`,
                error.stack
            );
            return {
                jobId: '',
                queueName: queue.name,
                status: 'failed',
                channels: [channel]
            };
        }
    }

    async enqueueBatch(jobs: CommunicationJobData[]): Promise<JobResponse[]> {
        this.logger.log(`Processing batch of ${jobs.length} jobs across multiple tenants`);

        const validationErrors: string[] = [];
        for (let i = 0; i < jobs.length; i++) {
            try {
                this.validateJobData(jobs[i]);
                this.validateTemplateVariables(jobs[i]);
            } catch (error) {
                validationErrors.push(`Job ${i + 1}: ${error.message}`);
            }
        }

        if (validationErrors.length > 0) {
            throw new BadRequestException(
                `Batch validation failed:\n${validationErrors.join('\n')}`
            );
        }

        const jobsByQueue = new Map<string, CommunicationJobData[]>();

        for (const job of jobs) {
            const queueKey = job.channels.length > 1 ? 'multi-channel' : job.channels[0];
            if (!jobsByQueue.has(queueKey)) {
                jobsByQueue.set(queueKey, []);
            }
            jobsByQueue.get(queueKey)!.push(job);
        }

        const results = await Promise.all(
            Array.from(jobsByQueue.entries()).map(async ([queueKey, queueJobs]) => {
                if (queueKey === 'multi-channel') {
                    return Promise.all(queueJobs.map((job) => this.enqueueMultiChannelJob(job)));
                } else {
                    return Promise.all(queueJobs.map((job) => this.enqueueSingleChannelJob(job)));
                }
            })
        );

        return results.flat();
    }

    async scheduleRecurringMessage(scheduledJobData: ScheduledJobData): Promise<JobResponse> {
        this.validateJobData(scheduledJobData);
        this.validateTemplateVariables(scheduledJobData);

        const queue =
            scheduledJobData.channels.length > 1
                ? this.multiChannelQueue
                : this.getQueueByChannel(scheduledJobData.channels[0]);

        const jobName = getJobName(
            scheduledJobData.tenantId,
            queue.name as QueueName,
            scheduledJobData.channels
        );

        try {
            const job = await queue.add(jobName, scheduledJobData, {
                repeat: {
                    pattern: scheduledJobData.cronExpression,
                    tz: scheduledJobData.timezone || 'UTC',
                    endDate: scheduledJobData.endDate
                },
                removeOnComplete: 10,
                removeOnFail: 5
            });

            this.logger.log(
                `Recurring message scheduled: ${job.id} for tenant ${scheduledJobData.tenantId}, channels: ${scheduledJobData.channels.join(', ')}, template: ${this.determineTemplateType(scheduledJobData.variables)}`
            );

            return {
                jobId: job.id?.toString() || 'unknown',
                queueName: queue.name,
                status: 'queued',
                channels: scheduledJobData.channels
            };
        } catch (error) {
            this.logger.error(
                `Failed to schedule recurring message: ${error.message}`,
                error.stack
            );
            throw new BadRequestException('Failed to schedule recurring message');
        }
    }

    private validateJobData(jobData: CommunicationJobData): void {
        if (!jobData.tenantId) {
            throw new BadRequestException('Tenant ID is required');
        }
        if (!jobData.userId) {
            throw new BadRequestException('User ID is required');
        }
        if (!jobData.channels || jobData.channels.length === 0) {
            throw new BadRequestException('At least one channel is required');
        }

        const supportedChannels = [
            COMMUNICATION_CHANNELS.EMAIL,
            COMMUNICATION_CHANNELS.NOTIFICATION,
            COMMUNICATION_CHANNELS.SMS
        ];
        const unsupportedChannels = jobData.channels.filter(
            (channel) => !supportedChannels.includes(channel)
        );
        if (unsupportedChannels.length > 0) {
            throw new BadRequestException(
                `Unsupported channels: ${unsupportedChannels.join(', ')}. Supported channels: ${supportedChannels.join(', ')}`
            );
        }

        for (const channel of jobData.channels) {
            const channelKey = channel.toLowerCase();
            if (!jobData.recipients[channelKey] || jobData.recipients[channelKey].length === 0) {
                throw new BadRequestException(`Recipients for channel ${channel} are required`);
            }

            if (channel === COMMUNICATION_CHANNELS.EMAIL) {
                const emailRecipients = jobData.recipients[channelKey];
                const invalidEmails = emailRecipients.filter((email) => !this.isValidEmail(email));
                if (invalidEmails.length > 0) {
                    throw new BadRequestException(
                        `Invalid email addresses: ${invalidEmails.join(', ')}`
                    );
                }
            }
        }

        if (!jobData.variables || typeof jobData.variables !== 'object') {
            throw new BadRequestException('Template variables are required and must be an object');
        }
    }

    private validateTemplateVariables(jobData: CommunicationJobData): void {
        const templateType = this.determineTemplateType(jobData.variables);

        try {
            validateTemplateVariables(templateType, jobData.variables);
        } catch (error) {
            throw new BadRequestException(
                `Template validation failed for ${templateType}: ${error.message}`
            );
        }

        if (jobData.channels.includes(COMMUNICATION_CHANNELS.EMAIL)) {
            this.validateEmailSpecificVariables(jobData.variables, templateType);
        }
    }

    private determineTemplateType(variables: Record<string, any>): keyof TemplateRequirements {
        if (variables.type && this.isValidTemplateType(variables.type)) {
            return variables.type;
        }

        if (variables.caseId && variables.urgency === 'high') return 'case-urgent';
        if (variables.caseId && variables.status) return 'case-update';
        if (variables.caseId && !variables.status) return 'case-created';
        if (variables.type === 'welcome' || variables.isWelcome) return 'welcome';
        if (variables.passwordResetToken || variables.resetUrl) return 'password-reset';
        if (variables.invitationToken || variables.inviteUrl) return 'user-invitation';
        if (variables.type === 'system_maintenance' || variables.maintenanceDate)
            return 'system-maintenance';
        if (variables.billingMonth || variables.invoiceNumber) return 'billing-statement';
        if (variables.dueDate && variables.amount) return 'payment-reminder';
        if (variables.reportPeriod || variables.type === 'weekly-report') return 'weekly-report';

        return 'generic-notification';
    }

    private isValidTemplateType(type: string): type is keyof TemplateRequirements {
        const validTypes: (keyof TemplateRequirements)[] = [
            'case-update',
            'case-urgent',
            'case-created',
            'welcome',
            'password-reset',
            'user-invitation',
            'billing-statement',
            'payment-reminder',
            'system-maintenance',
            'weekly-report',
            'generic-notification'
        ];
        return validTypes.includes(type as keyof TemplateRequirements);
    }

    private validateEmailSpecificVariables(
        variables: Record<string, any>,
        templateType: string
    ): void {
        if (!variables.recipientName && !variables.name && !variables.userName) {
            this.logger.warn(`No recipient name provided for ${templateType} template`);
        }

        if (templateType.startsWith('case-')) {
            if (!variables.caseId && !variables.caseNumber) {
                throw new BadRequestException(
                    'Case ID or case number is required for case templates'
                );
            }
        }

        if (templateType === 'password-reset') {
            if (!variables.resetUrl && !variables.passwordResetToken) {
                throw new BadRequestException(
                    'Reset URL or password reset token is required for password reset emails'
                );
            }
        }

        if (templateType === 'user-invitation') {
            if (!variables.inviteUrl && !variables.invitationToken) {
                throw new BadRequestException(
                    'Invite URL or invitation token is required for invitation emails'
                );
            }
        }

        if (templateType === 'billing-statement' || templateType === 'payment-reminder') {
            if (!variables.clientName) {
                throw new BadRequestException('Client name is required for billing templates');
            }
            if (!variables.amount && !variables.totalAmount && !variables.formattedAmount) {
                throw new BadRequestException(
                    'Amount information is required for billing templates'
                );
            }
        }
    }

    private isValidEmail(email: string): boolean {
        if (!email || typeof email !== 'string') {
            return false;
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email.trim());
    }

    private getQueueByChannel(channel: COMMUNICATION_CHANNELS): Queue {
        switch (channel) {
            case COMMUNICATION_CHANNELS.EMAIL:
                return this.emailQueue;
            case COMMUNICATION_CHANNELS.NOTIFICATION:
                return this.notificationQueue;
            default:
                throw new BadRequestException(`Unsupported channel: ${channel}`);
        }
    }

    private getRecipientForChannel(recipients: any, channel: string): string {
        const channelRecipients = recipients[channel.toLowerCase()];
        return channelRecipients && channelRecipients.length > 0 ? channelRecipients[0] : '';
    }

    private generateJobId(jobData: CommunicationJobData): string {
        return `${jobData.tenantId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    getTemplateRequirements(): TemplateRequirements {
        return {
            'case-update': {
                required: ['tenantName', 'recipientName', 'caseNumber', 'status'],
                optional: [
                    'caseSummary',
                    'nextSteps',
                    'handlerName',
                    'handlerTitle',
                    'urgency',
                    'deadline'
                ]
            },
            'case-urgent': {
                required: ['tenantName', 'recipientName', 'caseNumber', 'urgency'],
                optional: ['message', 'handlerName', 'courtDate', 'courtTime']
            },
            'case-created': {
                required: ['tenantName', 'recipientName', 'caseNumber'],
                optional: ['caseType', 'assignedLawyer', 'loginUrl']
            },
            welcome: {
                required: ['tenantName', 'recipientName'],
                optional: ['role', 'inviterName', 'loginUrl', 'assignedLawyer']
            },
            'password-reset': {
                required: ['tenantName', 'recipientName', 'resetUrl'],
                optional: ['expirationTime', 'securityTip']
            },
            'user-invitation': {
                required: ['tenantName', 'recipientName', 'inviteUrl'],
                optional: ['inviterName', 'role', 'loginUrl']
            },
            'billing-statement': {
                required: ['tenantName', 'clientName', 'billingMonth', 'formattedAmount'],
                optional: ['invoiceNumber', 'formattedDueDate', 'paymentUrl', 'itemizedCharges']
            },
            'payment-reminder': {
                required: ['tenantName', 'clientName', 'formattedAmount', 'formattedDueDate'],
                optional: ['invoiceNumber', 'paymentUrl', 'gracePeriod']
            },
            'system-maintenance': {
                required: ['tenantName', 'recipientName', 'formattedMaintenanceDate'],
                optional: ['maintenanceTime', 'expectedDowntime', 'affectedServices']
            },
            'weekly-report': {
                required: ['tenantName', 'recipientName', 'reportPeriod'],
                optional: ['casesSummary', 'reportUrl', 'newCases', 'closedCases', 'activeCases']
            },
            'generic-notification': {
                required: ['tenantName', 'recipientName', 'message'],
                optional: ['loginUrl', 'supportEmail']
            }
        };
    }

    getTemplateRequirementsForType(templateType: keyof TemplateRequirements): {
        required: string[];
        optional: string[];
    } {
        const requirements = this.getTemplateRequirements();
        return requirements[templateType];
    }
}
