import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Milestone, MilestoneStatus } from '../typeorm/entities/tenant/milestone.entity';
import { Task, TaskStatus } from '../typeorm/entities/tenant/task.entity';
import { CaseAuditService } from 'apps/case-management/src/services/case-audit.service';
import { CaseAuditAction } from '../typeorm/entities/tenant/case-audit.entity';
import { 
    DEFAULT_CONVEYANCING_MILESTONES, 
    DefaultMilestoneConfig, 
    getDefaultMilestonesByType 
} from '../constants/default-milestones.constants';

export interface MilestoneWithProgress extends Milestone {
    progressPercentage: number;
    taskSummary: {
        total: number;
        completed: number;
        inProgress: number;
        pending: number;
    };
}

export interface CaseStatistics {
    total: number;
    completed: number;
    inProgress: number;
    pending: number;
    averageProgress: number;
}

/**
 * Enhanced Milestone Service - Centralized milestone management
 * Consolidates all milestone functionality from multiple services
 */
@Injectable()
export class EnhancedMilestoneService {
    private readonly logger = new Logger(EnhancedMilestoneService.name);

    constructor(
        @InjectRepository(Milestone)
        private readonly milestoneRepository: Repository<Milestone>,
        @InjectRepository(Task)
        private readonly taskRepository: Repository<Task>,
        private readonly caseAuditService: CaseAuditService
    ) {}

    /**
     * Create default milestones and tasks for a new case
     * This is the main entry point for milestone creation
     */
    async createDefaultMilestonesForCase(
        caseId: string, 
        caseType: string, 
        createdBy: string
    ): Promise<Milestone[]> {
        const defaultConfig = getDefaultMilestonesByType(caseType);
        
        if (defaultConfig.length === 0) {
            this.logger.warn(`No default milestones found for case type: ${caseType}`);
            return [];
        }

        this.logger.log(`Creating ${defaultConfig.length} default milestones for case ${caseId} (type: ${caseType})`);

        const createdMilestones: Milestone[] = [];

        for (const milestoneConfig of defaultConfig) {
            const milestone = await this.createMilestoneWithTasks(
                caseId, 
                milestoneConfig, 
                createdBy
            );
            createdMilestones.push(milestone);
        }

        // Log milestone creation in audit trail
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.STATUS_CHANGED,
            createdBy,
            'System',
            'auto-generated',
            {
                action: 'DEFAULT_MILESTONES_CREATED',
                caseType,
                milestonesCount: createdMilestones.length,
                milestoneNames: createdMilestones.map(m => m.name)
            }
        );

        this.logger.log(`Successfully created ${createdMilestones.length} default milestones for case ${caseId}`);
        return createdMilestones;
    }

    /**
     * Create a single milestone with its tasks
     */
    private async createMilestoneWithTasks(
        caseId: string,
        config: DefaultMilestoneConfig,
        createdBy: string
    ): Promise<Milestone> {
        // Create milestone
        const milestone = this.milestoneRepository.create({
            name: config.name,
            description: config.description,
            caseId,
            sortOrder: config.sortOrder,
            status: MilestoneStatus.PENDING,
            isDefault: true,
            targetDate: config.targetDays 
                ? new Date(Date.now() + config.targetDays * 24 * 60 * 60 * 1000)
                : null,
            createdBy,
            progressPercentage: 0
        });

        const savedMilestone = await this.milestoneRepository.save(milestone);

        // Create tasks for this milestone
        for (const taskConfig of config.tasks) {
            const task = this.taskRepository.create({
                title: taskConfig.title,
                description: taskConfig.description,
                priority: taskConfig.priority,
                status: TaskStatus.OPEN,
                caseId,
                milestoneId: savedMilestone.id,
                isDefault: taskConfig.isDefault,
                dueDate: taskConfig.estimatedDays 
                    ? new Date(Date.now() + taskConfig.estimatedDays * 24 * 60 * 60 * 1000)
                    : null,
                createdBy
            });

            await this.taskRepository.save(task);
        }

        this.logger.log(`Created milestone '${milestone.name}' with ${config.tasks.length} tasks`);
        return savedMilestone;
    }

    /**
     * Get milestones with progress tracking for a case
     */
    async getMilestonesWithProgress(caseId: string): Promise<MilestoneWithProgress[]> {
        const milestones = await this.milestoneRepository.find({
            where: { caseId },
            relations: ['tasks'],
            order: { sortOrder: 'ASC' }
        });

        return milestones.map(milestone => this.calculateMilestoneProgress(milestone));
    }

    /**
     * Calculate progress for a single milestone
     */
    private calculateMilestoneProgress(milestone: Milestone): MilestoneWithProgress {
        const totalTasks = milestone.tasks?.length || 0;
        const completedTasks = milestone.tasks?.filter(task => task.status === TaskStatus.DONE).length || 0;
        const inProgressTasks = milestone.tasks?.filter(task => task.status === TaskStatus.IN_PROGRESS).length || 0;
        
        const progressPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
        
        // Determine milestone status based on task completion
        let status = MilestoneStatus.PENDING;
        if (completedTasks === totalTasks && totalTasks > 0) {
            status = MilestoneStatus.COMPLETED;
        } else if (completedTasks > 0 || inProgressTasks > 0) {
            status = MilestoneStatus.IN_PROGRESS;
        }

        return {
            ...milestone,
            progressPercentage,
            status,
            taskSummary: {
                total: totalTasks,
                completed: completedTasks,
                inProgress: inProgressTasks,
                pending: totalTasks - completedTasks - inProgressTasks
            }
        };
    }

    /**
     * Update milestone progress when a task status changes
     * This is called by the task service when task status changes
     */
    async updateMilestoneProgress(milestoneId: string, triggeredByTaskId?: string): Promise<void> {
        const milestone = await this.milestoneRepository.findOne({
            where: { id: milestoneId },
            relations: ['tasks']
        });

        if (!milestone) {
            this.logger.warn(`Milestone ${milestoneId} not found for progress update`);
            return;
        }

        const oldStatus = milestone.status;
        const oldProgress = milestone.progressPercentage;

        // Calculate new progress
        const progressData = this.calculateMilestoneProgress(milestone);
        
        // Update milestone with new progress
        milestone.progressPercentage = progressData.progressPercentage;
        milestone.status = progressData.status;
        
        // Set completion date if milestone is completed
        if (progressData.status === MilestoneStatus.COMPLETED && !milestone.completionDate) {
            milestone.completionDate = new Date();
        } else if (progressData.status !== MilestoneStatus.COMPLETED) {
            milestone.completionDate = null;
        }

        milestone.updatedAt = new Date();
        await this.milestoneRepository.save(milestone);

        // Log significant progress changes
        if (oldStatus !== milestone.status || Math.abs(oldProgress - milestone.progressPercentage) >= 10) {
            await this.caseAuditService.logAction(
                milestone.caseId,
                CaseAuditAction.STATUS_CHANGED,
                'system',
                'System',
                'auto-generated',
                {
                    action: 'MILESTONE_PROGRESS_UPDATED',
                    milestoneId: milestone.id,
                    milestoneName: milestone.name,
                    oldStatus,
                    newStatus: milestone.status,
                    oldProgress,
                    newProgress: milestone.progressPercentage,
                    triggeredByTaskId
                }
            );
        }

        this.logger.debug(`Updated milestone ${milestone.name} progress: ${milestone.progressPercentage}% (${milestone.status})`);
    }

    /**
     * Get milestone by ID with tasks
     */
    async getMilestoneById(id: string): Promise<Milestone> {
        const milestone = await this.milestoneRepository.findOne({
            where: { id },
            relations: ['tasks']
        });

        if (!milestone) {
            throw new NotFoundException(`Milestone with ID ${id} not found`);
        }

        return milestone;
    }

    /**
     * Get case statistics
     */
    async getCaseStatistics(caseId: string): Promise<CaseStatistics> {
        const milestones = await this.getMilestonesWithProgress(caseId);
        
        const stats: CaseStatistics = {
            total: milestones.length,
            completed: milestones.filter(m => m.status === MilestoneStatus.COMPLETED).length,
            inProgress: milestones.filter(m => m.status === MilestoneStatus.IN_PROGRESS).length,
            pending: milestones.filter(m => m.status === MilestoneStatus.PENDING).length,
            averageProgress: 0
        };

        if (stats.total > 0) {
            const totalProgress = milestones.reduce((sum, m) => sum + (m.progressPercentage || 0), 0);
            stats.averageProgress = Math.round(totalProgress / stats.total * 100) / 100;
        }

        return stats;
    }

    /**
     * Add a custom task to an existing milestone
     */
    async addTaskToMilestone(
        milestoneId: string,
        taskData: Partial<Task>,
        userId: string,
        userName: string
    ): Promise<Task> {
        const milestone = await this.getMilestoneById(milestoneId);
        
        const task = this.taskRepository.create({
            ...taskData,
            milestoneId: milestoneId,
            caseId: milestone.caseId,
            isDefault: false, // Custom tasks are not default
            createdBy: userId
        });

        const savedTask = await this.taskRepository.save(task);

        // Log task addition
        await this.caseAuditService.logAction(
            milestone.caseId,
            CaseAuditAction.STATUS_CHANGED,
            userId,
            userName,
            'user-action',
            {
                action: 'CUSTOM_TASK_ADDED_TO_MILESTONE',
                milestoneId: milestoneId,
                milestoneName: milestone.name,
                taskId: savedTask.id,
                taskTitle: savedTask.title
            }
        );

        // Update milestone progress after adding task
        await this.updateMilestoneProgress(milestoneId, savedTask.id);

        return savedTask;
    }
}
