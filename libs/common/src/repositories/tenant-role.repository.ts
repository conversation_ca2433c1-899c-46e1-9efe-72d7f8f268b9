import { Injectable } from '@nestjs/common';
import { DeepPartial } from 'typeorm';
import { TenantRole } from '@app/common/typeorm/entities/tenant/tenant-role.entity';
import { UserProfile } from '@app/common/typeorm/entities/tenant/user-profile.entity';
import { TenantConnectionService } from '@app/common/multi-tenancy';
import { TenantContextService } from '@app/common/multi-tenancy';
import { BaseTenantRepository } from '@app/common/multi-tenancy';

@Injectable()
export class TenantRoleRepository extends BaseTenantRepository<TenantRole> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(TenantRole, tenantContextService, tenantConnectionService);
    }

    async create(roleData: {
        name: string;
        description?: string;
        permissions?: Record<string, any>;
    }): Promise<TenantRole> {
        const role = await super.create(roleData);
        return this.save(role as DeepPartial<TenantRole>);
    }

    async findByName(name: string): Promise<TenantRole | null> {
        return this.findOne({ where: { name } });
    }

    async createUserProfile(profileData: {
        username: string;
        email: string;
        firstName: string;
        lastName: string;
        userId: string;
        keycloakId?: string;
        additionalInfo?: Record<string, any>;
        roles?: string[];
    }): Promise<DeepPartial<UserProfile>> {
        const tenantId = this.tenantContextService.getTenantId();

        if (!tenantId) {
            throw new Error('Tenant ID not found');
        }

        const schemaName = this.tenantContextService.getTenantSchema();

        const tenantDataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);

        const result = await tenantDataSource.query(
            `
            INSERT INTO "${schemaName}"."user_profiles" ("username", "email", "first_name", "last_name", "additional_info", "user_id", "keycloak_id")
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `,
            [
                profileData.username,
                profileData.email,
                profileData.firstName,
                profileData.lastName,
                profileData.additionalInfo,
                profileData.userId,
                profileData.keycloakId
            ]
        );

        const userId = result[0].id;
        const adminRole = await this.findByName(profileData?.roles?.[0] ?? 'ADMIN');

        if (!adminRole) {
            throw new Error('ADMIN role not found');
        }

        await tenantDataSource
            .createQueryBuilder()
            .insert()
            .into('user_roles')
            .values({
                user_id: userId,
                role_id: adminRole.id
            })
            .execute();

        const savedUserProfile = await tenantDataSource
            .getRepository(UserProfile)
            .findOne({ where: { id: userId } });

        if (!savedUserProfile) {
            throw new Error('Error creating user profile');
        }

        return savedUserProfile;
    }

    async assignRoleToUser(userId: string, roleId: string): Promise<void> {
        await this.query(
            `INSERT INTO "user_roles" ("user_id", "role_id") VALUES ($1, $2)
             ON CONFLICT ("user_id", "role_id") DO NOTHING`,
            [userId, roleId]
        );
    }

    async removeRoleFromUser(userId: string, roleId: string): Promise<void> {
        await this.query(`DELETE FROM "user_roles" WHERE "user_id" = $1 AND "role_id" = $2`, [
            userId,
            roleId
        ]);
    }

    async getUserProfile(userId: string): Promise<DeepPartial<UserProfile> | null> {
        const tenantId = this.tenantContextService.getTenantId();
        if (!tenantId) {
            throw new Error('Tenant ID not found');
        }

        const schemaName = this.tenantContextService.getTenantSchema();
        const tenantDataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);

        try {
            // Direct SQL query to check if user profile exists by user_id
            const result = await tenantDataSource.query(
                `SELECT * FROM "${schemaName}"."user_profiles" WHERE "user_id" = $1 LIMIT 1`,
                [userId]
            );

            if (result && result.length > 0) {
                return result[0];
            }

            return null;
        } catch (error) {
            this.logger.error(`Error checking for user profile with user_id ${userId}`, error);
            return null;
        }
    }

    async findUserProfileByUserId(userId: string): Promise<UserProfile | null> {
        const tenantId = this.tenantContextService.getTenantId();
        if (!tenantId) {
            throw new Error('Tenant ID not found');
        }

        const tenantDataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const userProfileRepository = tenantDataSource.getRepository(UserProfile);

        return userProfileRepository.findOne({
            where: { userId: userId }
        });
    }

    async getUserRoles(userId: string): Promise<TenantRole[]> {
        const tenantId = this.tenantContextService.getTenantId();
        if (!tenantId) {
            throw new Error('Tenant ID not found');
        }

        const schemaName = this.tenantContextService.getTenantSchema();

        // Check if userProfile exists first - look by user_id, not id
        const userProfile = await this.query(
            `
            SELECT * FROM "${schemaName}"."user_profiles" WHERE "user_id" = $1
        `,
            [userId]
        );

        if (!userProfile || userProfile.length === 0) {
            return []; // Return empty array if user not found
        }

        return await this.find({
            where: {
                users: {
                    id: userProfile[0].id // Now this is the correct user profile ID
                }
            }
        });
    }
}
