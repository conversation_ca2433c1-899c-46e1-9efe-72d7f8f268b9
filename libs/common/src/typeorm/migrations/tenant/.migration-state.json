{"lastMigration": "1752763240011-initialtenantmigrations.ts", "timestamp": 1752763240023, "entities": [{"tableName": "tenant_roles", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": true, "unique": true, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "permissions", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "enabled", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "users", "type": "many-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "user_profiles"}}], "indices": []}, {"tableName": "user_profiles", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "username", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "email", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "first_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "department", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "last_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "enabled", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "email_verified", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "keycloak_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "additional_info", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "'{}'", "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "user_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "roles", "type": "many-to-many", "joinColumns": [{"databaseName": "user_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tenant_roles"}}], "indices": []}, {"tableName": "case_notes", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "content", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "is_pinned", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "is_private", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_attachments", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "filename", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "url", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "file_size", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "mime_type", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_type", "type": "case_attachments_document_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["PLEADING", "CONTRACT", "EVIDENCE", "CORRESPONDENCE", "COURT_ORDER", "INVOICE", "MEMO", "RESEARCH", "OTHER"], "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_audit", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "action", "type": "case_audit_action_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["CREATED", "UPDATED", "CLIENT_UPDATED", "STATUS_CHANGED", "STATUS_DRAFT", "STATUS_SUBMITTED", "STATUS_APPROVED", "STATUS_DECLINED", "STATUS_IN_PROGRESS", "STATUS_ON_HOLD", "STATUS_CLOSED", "STATUS_ARCHIVED", "ASSIGNED", "UNASSIGNED", "REASSIGNED", "NOTE_ADDED", "NOTE_UPDATED", "NOTE_DELETED", "ATTACHMENT_ADDED", "ATTACHMENT_UPDATED", "ATTACHMENT_REMOVED", "CONTACT_ADDED", "CONTACT_UPDATED", "CONTACT_DELETED", "CASE_RELATION_ADDED", "CASE_RELATION_DELETED", "ACCESSED", "ACCESS_DENIED", "EXPORTED", "CLIENT_CREATED", "REMINDER_SET", "DEADLINE_APPROACHING", "DEADLINE_MISSED", "STATUS_UNDER_REVIEW", "STATUS_ASSIGNED", "STATUS_PENDING_APPROVAL", "STATUS_REJECTED", "STATUS_RESOLVED", "STATUS_REOPENED", "PERMISSION_CHECK", "DEADLINE_MISSED"], "length": ""}, {"databaseName": "performed_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "ip_address", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "details", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_contacts", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "email", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "phone", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "address", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "type", "type": "case_contacts_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["CLIENT", "CO_COUNSEL", "OPPOSING_COUNSEL", "WITNESS", "EXPERT", "JUDGE", "OTHER"], "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "additional_info", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_events", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "category", "type": "case_events_category_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["INTAKE", "PLEADINGS", "DISCOVERY", "MOTIONS", "HEARINGS", "TRIAL", "POST_TRIAL", "DEADLINE", "ADMINISTRATIVE", "COMMUNICATION", "OTHER"], "length": ""}, {"databaseName": "type", "type": "case_events_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["INITIAL_CONSULTATION", "CONFLICT_CHECK", "ENGAGEMENT_LETTER", "CASE_OPENED", "STATUTE_OF_LIMITATIONS", "COMPLAINT_FILED", "SUMMONS_ISSUED", "SUMMONS_SERVED", "ANSWER_FILED", "COUNTERCLAIM_FILED", "CROSS_CLAIM_FILED", "AMENDED_PLEADING", "INTERROGATORIES_SERVED", "INTERROGATORIES_ANSWERED", "DOCUMENT_REQUEST_SERVED", "DOCUMENT_PRODUCTION", "DEPOSITION_SCHEDULED", "DEPOSITION_COMPLETED", "EXPERT_DISCLOSURE", "DISCOVERY_CUTOFF", "DISCOVERY_DISPUTE", "MOTION_TO_COMPEL", "MOTION_TO_DISMISS", "SUMMARY_JUDGMENT_MOTION", "MOTION_HEARING", "MOTION_IN_LIMINE", "CASE_MANAGEMENT_CONFERENCE", "STATUS_CONFERENCE", "SETTLEMENT_CONFERENCE", "MEDIATION", "PRE_TRIAL_CONFERENCE", "TRIAL_START", "TRIAL_END", "WITNESS_LIST_FILED", "EXHIBIT_LIST_FILED", "JURY_SELECTION", "OPENING_ARGUMENTS", "CLOSING_ARGUMENTS", "VERDICT", "JUDGMENT_ENTERED", "POST_TRIAL_MOTION", "NOTICE_OF_APPEAL", "APPELLATE_BRIEF", "ORAL_ARGUMENTS", "APPELLATE_DECISION", "COURT_DEADLINE", "INTERNAL_DEADLINE", "RESPONSE_DEADLINE", "COURT_FEE_PAYMENT", "BILLING_MILESTONE", "COMPLIANCE_AUDIT", "CLIENT_MEETING", "CLIENT_CALL", "TEAM_MEETING", "OPPOSING_COUNSEL_COMMUNICATION", "COURT_COMMUNICATION", "OTHER"], "length": ""}, {"databaseName": "title", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "event_date", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "clients", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "email", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "phone", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "address", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "additional_info", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "cases", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "cases", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_number", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": true, "unique": true, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "title", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "status", "type": "cases_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "DRAFT", "enum": ["DRAFT", "SUBMITTED", "APPROVED", "DECLINED", "IN_PROGRESS", "ON_HOLD", "CLOSED", "ARCHIVED"], "length": ""}, {"databaseName": "priority", "type": "cases_priority_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "MEDIUM", "enum": ["LOW", "MEDIUM", "HIGH", "URGENT"], "length": ""}, {"databaseName": "type", "type": "cases_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["LITIGATION", "CORPORATE", "REAL_ESTATE", "INTELLECTUAL_PROPERTY", "FAMILY", "CRIMINAL", "OTHER"], "length": ""}, {"databaseName": "client_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "deadline", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "client", "type": "many-to-one", "joinColumns": [{"databaseName": "client_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "clients"}}, {"propertyName": "assignments", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_assignments"}}, {"propertyName": "notes", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_notes"}}, {"propertyName": "attachments", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_attachments"}}, {"propertyName": "auditTrail", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_audit"}}, {"propertyName": "contacts", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_contacts"}}, {"propertyName": "events", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_events"}}], "indices": []}, {"tableName": "case_assignments", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_id", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "assigned_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "assigned_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "is_active", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "notes", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_relations", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "related_case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "type", "type": "case_relations_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "RELATED", "enum": ["PARENT", "CHILD", "RELATED", "PREDECESSOR", "SUCCESSOR"], "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "notes", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "task_history", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "task_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "from_status", "type": "task_status", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enumName": "task_status", "length": ""}, {"databaseName": "to_status", "type": "task_status", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enumName": "task_status", "length": ""}, {"databaseName": "changed_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "changed_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "changed_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "task", "type": "many-to-one", "joinColumns": [{"databaseName": "task_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tasks"}}], "indices": []}, {"tableName": "task_dependencies", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "task_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "depends_on_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "task", "type": "many-to-one", "joinColumns": [{"databaseName": "task_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tasks"}}, {"propertyName": "dependsOn", "type": "many-to-one", "joinColumns": [{"databaseName": "depends_on_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tasks"}}], "indices": []}, {"tableName": "task_history", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "task_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "from_status", "type": "task_status", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enumName": "task_status", "length": ""}, {"databaseName": "to_status", "type": "task_status", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enumName": "task_status", "length": ""}, {"databaseName": "changed_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "changed_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "changed_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "task", "type": "many-to-one", "joinColumns": [{"databaseName": "task_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tasks"}}], "indices": []}, {"tableName": "tasks", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "title", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "status", "type": "task_status", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OPEN", "enum": ["OPEN", "IN_PROGRESS", "BLOCKED", "DONE"], "enumName": "task_status", "length": ""}, {"databaseName": "priority", "type": "task_priority", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "MEDIUM", "enum": ["HIGHEST", "HIGH", "MEDIUM", "LOW", "LOWEST"], "enumName": "task_priority", "length": ""}, {"databaseName": "due_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "assignee_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "dependencies", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "task_dependencies"}}, {"propertyName": "dependents", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "task_dependencies"}}, {"propertyName": "statusHistory", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "task_history"}}], "indices": []}, {"tableName": "documents", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "folder_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "s3_key", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "s3_bucket", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "file_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "file_extension", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "mime_type", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "size_in_bytes", "type": "bigint", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "checksum", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "last_modified_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "folder", "type": "many-to-one", "joinColumns": [{"databaseName": "folder_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "document_folders"}}], "indices": []}, {"tableName": "document_folders", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "parent_folder_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "path", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "parentFolder", "type": "many-to-one", "joinColumns": [{"databaseName": "parent_folder_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "document_folders"}}, {"propertyName": "childFolders", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "document_folders"}}, {"propertyName": "documents", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}, {"tableName": "document_access", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_id", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "permission_level", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "read", "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "expires_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "document", "type": "many-to-one", "joinColumns": [{"databaseName": "document_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}, {"tableName": "document_audit", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "action_type", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "action_details", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "ip_address", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_agent", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_version_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "document", "type": "many-to-one", "joinColumns": [{"databaseName": "document_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}, {"tableName": "document_workflows", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "workflow_type", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "current_state", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "workflow_data", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "completed_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "due_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "assigned_to", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "document", "type": "many-to-one", "joinColumns": [{"databaseName": "document_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}]}